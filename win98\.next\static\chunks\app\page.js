/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cwin98%5C%5Cwin98%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cwin98%5C%5Cwin98%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3dpbjk4JTVDJTVDd2luOTglNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUF5RSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcd2luOThcXFxcd2luOThcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cwin98%5C%5Cwin98%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/compiled/client-only/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {



/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkU6XFx3aW45OFxcd2luOThcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/styled-jsx/dist/index/index.js ***!
  \*****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n__webpack_require__(/*! client-only */ \"(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js\");\nvar React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === 'object' && 'default' in e ? e : {\n        'default': e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n_c = React__default;\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && \"development\" === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        }) // Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        }) // filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    _s();\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState({\n        \"StyleRegistry.useState[ref]\": function() {\n            return rootRegistry || configuredRegistry || createStyleRegistry();\n        }\n    }[\"StyleRegistry.useState[ref]\"]), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\n_s(StyleRegistry, \"F6PIZFsaWgcE6rBNmd+Zkq3zRoY=\");\n_c1 = StyleRegistry;\nfunction useStyleRegistry() {\n    _s1();\n    return React.useContext(StyleSheetContext);\n}\n_s1(useStyleRegistry, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    _s2();\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect({\n        \"JSXStyle.useInsertionEffect\": function() {\n            registry.add(props);\n            return ({\n                \"JSXStyle.useInsertionEffect\": function() {\n                    registry.remove(props);\n                }\n            })[\"JSXStyle.useInsertionEffect\"];\n        // props.children can be string[], will be striped since id is identical\n        }\n    }[\"JSXStyle.useInsertionEffect\"], [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\n_s2(JSXStyle, \"48Sqj1BUqkshsPdz6NEWXDn8pF4=\", false, function() {\n    return [\n        useStyleRegistry,\n        useInsertionEffect\n    ];\n});\n_c2 = JSXStyle;\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"React__default\");\n$RefreshReg$(_c1, \"StyleRegistry\");\n$RefreshReg$(_c2, \"JSXStyle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/style.js":
/*!******************************************!*\
  !*** ./node_modules/styled-jsx/style.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nmodule.exports = __webpack_require__(/*! ./dist/index */ \"(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\").style;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9zdHlsZWQtanN4L3N0eWxlLmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsbUlBQThDIiwic291cmNlcyI6WyJFOlxcd2luOThcXHdpbjk4XFxub2RlX21vZHVsZXNcXHN0eWxlZC1qc3hcXHN0eWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2luZGV4Jykuc3R5bGVcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSIsInN0eWxlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/style.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/ChatWindow.tsx":
/*!*******************************************!*\
  !*** ./src/app/components/ChatWindow.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ChatWindow(param) {\n    let { title, onClose, initialPosition } = param;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            sender: 'assistant',\n            content: 'Hi! I\\'m Sheila. How can I help you today?',\n            timestamp: new Date()\n        }\n    ]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"ChatWindow.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"ChatWindow.useEffect\"];\n            }\n        }\n    }[\"ChatWindow.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            sender: 'user',\n            content: inputValue,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputValue('');\n        setIsLoading(true);\n        try {\n            // Send entire conversation history for context\n            const conversationHistory = [\n                ...messages,\n                userMessage\n            ].map((msg)=>({\n                    role: msg.sender === 'user' ? 'user' : 'assistant',\n                    content: msg.content\n                }));\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    conversation: conversationHistory\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get response');\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                sender: 'assistant',\n                content: data.message,\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                sender: 'assistant',\n                content: 'Sorry, I encountered an error. Please try again.',\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        className: \"win98-window win98-chat-window\",\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            zIndex: 100\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                className: \"win98-title-bar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-title-text\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-window-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"_\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"□\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"win98-chat-content\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"chat-message \".concat(message.sender),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"message-sender\",\n                                    children: message.sender === 'user' ? 'You' : 'Sheila'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"message-content\",\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-message assistant\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"message-sender\",\n                                children: \"Sheila\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"message-content\",\n                                children: \"Typing...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"win98-chat-input-area\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        className: \"win98-chat-input\",\n                        value: inputValue,\n                        onChange: (e)=>setInputValue(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        placeholder: \"Type your message...\",\n                        disabled: isLoading\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"win98-button\",\n                        onClick: handleSendMessage,\n                        disabled: isLoading || !inputValue.trim(),\n                        children: \"Send\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\ChatWindow.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWindow, \"tb7qjLURJhvULX7dKEP3SwSEyjg=\");\n_c = ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/ChatWindow.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/DesktopIcon.tsx":
/*!********************************************!*\
  !*** ./src/app/components/DesktopIcon.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesktopIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DesktopIcon(param) {\n    let { icon, label, position, onDoubleClick } = param;\n    _s();\n    const [isSelected, setIsSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clickCount, setClickCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleClick = (e)=>{\n        e.stopPropagation(); // Prevent desktop click\n        setIsSelected(true);\n        setClickCount((prev)=>prev + 1);\n        // Reset click count after 300ms if no second click\n        setTimeout(()=>{\n            if (clickCount === 0) {\n                setClickCount(0);\n            }\n        }, 300);\n        // Handle double click\n        if (clickCount === 1) {\n            onDoubleClick();\n            setClickCount(0);\n        }\n    };\n    // Listen for clicks outside to deselect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesktopIcon.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"DesktopIcon.useEffect.handleClickOutside\": ()=>{\n                    setIsSelected(false);\n                }\n            }[\"DesktopIcon.useEffect.handleClickOutside\"];\n            document.addEventListener('click', handleClickOutside);\n            return ({\n                \"DesktopIcon.useEffect\": ()=>{\n                    document.removeEventListener('click', handleClickOutside);\n                }\n            })[\"DesktopIcon.useEffect\"];\n        }\n    }[\"DesktopIcon.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"win98-desktop-icon \".concat(isSelected ? 'selected' : ''),\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\")\n        },\n        onClick: handleClick,\n        onBlur: ()=>setIsSelected(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"win98-icon-image\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"win98-icon-label\",\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(DesktopIcon, \"TeF6O8Dwy0cOoLnA7CxZcXBh7Wc=\");\n_c = DesktopIcon;\nvar _c;\n$RefreshReg$(_c, \"DesktopIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/DesktopIcon.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/Solitaire.tsx":
/*!******************************************!*\
  !*** ./src/app/components/Solitaire.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Solitaire)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Solitaire(param) {\n    let { onClose, initialPosition } = param;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [deck, setDeck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tableau, setTableau] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        []\n    ]);\n    const [stock, setStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [waste, setWaste] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [foundations, setFoundations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        [],\n        [],\n        [],\n        []\n    ]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const suits = [\n        '♠',\n        '♥',\n        '♦',\n        '♣'\n    ];\n    const values = [\n        'A',\n        '2',\n        '3',\n        '4',\n        '5',\n        '6',\n        '7',\n        '8',\n        '9',\n        '10',\n        'J',\n        'Q',\n        'K'\n    ];\n    const createDeck = ()=>{\n        const newDeck = [];\n        suits.forEach((suit)=>{\n            values.forEach((value)=>{\n                newDeck.push({\n                    suit,\n                    value,\n                    color: suit === '♥' || suit === '♦' ? 'red' : 'black',\n                    faceUp: false,\n                    id: \"\".concat(suit, \"-\").concat(value)\n                });\n            });\n        });\n        return shuffleDeck(newDeck);\n    };\n    const shuffleDeck = (deck)=>{\n        const shuffled = [\n            ...deck\n        ];\n        for(let i = shuffled.length - 1; i > 0; i--){\n            const j = Math.floor(Math.random() * (i + 1));\n            [shuffled[i], shuffled[j]] = [\n                shuffled[j],\n                shuffled[i]\n            ];\n        }\n        return shuffled;\n    };\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Solitaire.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Solitaire.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Solitaire.useEffect\"];\n            }\n        }\n    }[\"Solitaire.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    const startNewGame = ()=>{\n        const newDeck = createDeck();\n        // Deal cards to tableau\n        const newTableau = [\n            [],\n            [],\n            [],\n            [],\n            [],\n            [],\n            []\n        ];\n        let deckIndex = 0;\n        for(let col = 0; col < 7; col++){\n            for(let row = 0; row <= col; row++){\n                const card = newDeck[deckIndex++];\n                if (row === col) {\n                    card.faceUp = true; // Top card is face up\n                }\n                newTableau[col].push(card);\n            }\n        }\n        // Remaining cards go to stock\n        const remainingCards = newDeck.slice(deckIndex);\n        setTableau(newTableau);\n        setStock(remainingCards);\n        setWaste([]);\n        setFoundations([\n            [],\n            [],\n            [],\n            []\n        ]);\n        setScore(0);\n        setGameStarted(true);\n    };\n    const CardComponent = (param)=>{\n        let { card, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"solitaire-card\",\n            onClick: onClick,\n            style: {\n                width: '50px',\n                height: '70px',\n                border: '1px solid var(--win98-black)',\n                background: card.faceUp ? 'white' : 'var(--win98-blue)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                color: card.faceUp ? card.color : 'white',\n                cursor: 'pointer',\n                margin: '2px'\n            },\n            children: card.faceUp ? \"\".concat(card.value).concat(card.suit) : '🂠'\n        }, void 0, false, {\n            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n            lineNumber: 127,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        className: \"win98-window\",\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '500px',\n            height: '400px',\n            zIndex: 100\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                className: \"win98-title-bar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-title-text\",\n                        children: \"\\uD83C\\uDCCF Solitaire\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-window-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"_\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"□\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '8px',\n                    background: 'var(--win98-gray)',\n                    height: 'calc(100% - 18px)',\n                    overflow: 'auto'\n                },\n                children: !gameStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        height: '100%'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                marginBottom: '20px'\n                            },\n                            children: \"Windows Solitaire\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"win98-button\",\n                            onClick: startNewGame,\n                            children: \"New Game\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '20px',\n                                fontSize: '11px',\n                                textAlign: 'center',\n                                color: 'var(--win98-shadow-dark)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Classic Klondike Solitaire\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Click cards to flip them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Drag cards to move them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"win98-button\",\n                                    onClick: startNewGame,\n                                    children: \"New Game\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginLeft: '20px',\n                                        fontSize: '11px'\n                                    },\n                                    children: [\n                                        \"Cards: \",\n                                        deck.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'var(--win98-desktop)',\n                                minHeight: '300px',\n                                border: '2px inset var(--win98-gray)',\n                                padding: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '10px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '5px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px'\n                                                        },\n                                                        children: \"Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px'\n                                                        },\n                                                        children: \"Waste\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '5px'\n                                            },\n                                            children: suits.map((suit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"win98-border-inset\",\n                                                    style: {\n                                                        width: '54px',\n                                                        height: '74px',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: suit\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '10px',\n                                        justifyContent: 'center'\n                                    },\n                                    children: [\n                                        1,\n                                        2,\n                                        3,\n                                        4,\n                                        5,\n                                        6,\n                                        7\n                                    ].map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column'\n                                            },\n                                            children: deck.slice(0, column).map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardComponent, {\n                                                    card: {\n                                                        ...card,\n                                                        faceUp: index === column - 1\n                                                    },\n                                                    onClick: ()=>{\n                                                        // Simple card flip logic\n                                                        const newDeck = [\n                                                            ...deck\n                                                        ];\n                                                        newDeck[index].faceUp = !newDeck[index].faceUp;\n                                                        setDeck(newDeck);\n                                                    }\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, column, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(Solitaire, \"/kxWNHzc9vXkpHhiXjDak8TF2cY=\");\n_c = Solitaire;\nvar _c;\n$RefreshReg$(_c, \"Solitaire\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Solitaire.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/Taskbar.tsx":
/*!****************************************!*\
  !*** ./src/app/components/Taskbar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Taskbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Taskbar() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [isStartMenuOpen, setIsStartMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Taskbar.useEffect\": ()=>{\n            const updateTime = {\n                \"Taskbar.useEffect.updateTime\": ()=>{\n                    setCurrentTime(new Date().toLocaleTimeString('en-US', {\n                        hour: '2-digit',\n                        minute: '2-digit',\n                        hour12: false\n                    }));\n                }\n            }[\"Taskbar.useEffect.updateTime\"];\n            updateTime();\n            const interval = setInterval(updateTime, 1000);\n            return ({\n                \"Taskbar.useEffect\": ()=>clearInterval(interval)\n            })[\"Taskbar.useEffect\"];\n        }\n    }[\"Taskbar.useEffect\"], []);\n    const toggleStartMenu = ()=>{\n        setIsStartMenuOpen(!isStartMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isStartMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    bottom: '28px',\n                    left: '0',\n                    width: '200px',\n                    background: 'var(--win98-gray)',\n                    border: '2px outset var(--win98-gray)',\n                    zIndex: 1001\n                },\n                className: \"jsx-7d636cc268a2172f\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(90deg, var(--win98-dark-blue) 0%, var(--win98-blue) 100%)',\n                            color: 'white',\n                            padding: '8px',\n                            fontSize: '14px',\n                            fontWeight: 'bold'\n                        },\n                        className: \"jsx-7d636cc268a2172f\",\n                        children: \"Windows 98\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '4px'\n                        },\n                        className: \"jsx-7d636cc268a2172f\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7d636cc268a2172f\" + \" \" + \"start-menu-item\",\n                                children: \"\\uD83D\\uDCC1 Programs\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7d636cc268a2172f\" + \" \" + \"start-menu-item\",\n                                children: \"\\uD83D\\uDCC4 Documents\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7d636cc268a2172f\" + \" \" + \"start-menu-item\",\n                                children: \"⚙️ Settings\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7d636cc268a2172f\" + \" \" + \"start-menu-item\",\n                                children: \"\\uD83D\\uDD0D Find\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7d636cc268a2172f\" + \" \" + \"start-menu-item\",\n                                children: \"❓ Help\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7d636cc268a2172f\" + \" \" + \"start-menu-item\",\n                                children: \"\\uD83C\\uDFC3 Run...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                style: {\n                                    margin: '4px 0',\n                                    border: '1px inset var(--win98-gray)'\n                                },\n                                className: \"jsx-7d636cc268a2172f\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-7d636cc268a2172f\" + \" \" + \"start-menu-item\",\n                                children: \"⏻ Shut Down...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-7d636cc268a2172f\" + \" \" + \"win98-taskbar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleStartMenu,\n                        style: {\n                            background: isStartMenuOpen ? 'var(--win98-border-inset)' : 'var(--win98-gray)'\n                        },\n                        className: \"jsx-7d636cc268a2172f\" + \" \" + \"win98-start-button\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-7d636cc268a2172f\",\n                                children: \"\\uD83E\\uDE9F\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            \"Start\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        },\n                        className: \"jsx-7d636cc268a2172f\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '2px 8px',\n                            fontSize: '11px',\n                            background: 'var(--win98-gray)',\n                            minWidth: '60px',\n                            textAlign: 'center'\n                        },\n                        className: \"jsx-7d636cc268a2172f\" + \" \" + \"win98-border-inset\",\n                        children: currentTime\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Taskbar.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"7d636cc268a2172f\",\n                children: \".start-menu-item.jsx-7d636cc268a2172f{padding:4px 8px;cursor:pointer;font-size:11px}.start-menu-item.jsx-7d636cc268a2172f:hover{background:var(--win98-highlight);color:white}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(Taskbar, \"6Olt1ZwnAkbxdWl+UL9UFiNBkbo=\");\n_c = Taskbar;\nvar _c;\n$RefreshReg$(_c, \"Taskbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29tcG9uZW50cy9UYXNrYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFNEM7QUFFN0IsU0FBU0U7O0lBQ3RCLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHSiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNLLGlCQUFpQkMsbUJBQW1CLEdBQUdOLCtDQUFRQSxDQUFDO0lBRXZEQyxnREFBU0E7NkJBQUM7WUFDUixNQUFNTTtnREFBYTtvQkFDakJILGVBQWUsSUFBSUksT0FBT0Msa0JBQWtCLENBQUMsU0FBUzt3QkFDcERDLE1BQU07d0JBQ05DLFFBQVE7d0JBQ1JDLFFBQVE7b0JBQ1Y7Z0JBQ0Y7O1lBRUFMO1lBQ0EsTUFBTU0sV0FBV0MsWUFBWVAsWUFBWTtZQUN6QztxQ0FBTyxJQUFNUSxjQUFjRjs7UUFDN0I7NEJBQUcsRUFBRTtJQUVMLE1BQU1HLGtCQUFrQjtRQUN0QlYsbUJBQW1CLENBQUNEO0lBQ3RCO0lBRUEscUJBQ0U7O1lBRUdBLGlDQUNDLDhEQUFDWTtnQkFBSUMsT0FBTztvQkFDVkMsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUkMsTUFBTTtvQkFDTkMsT0FBTztvQkFDUEMsWUFBWTtvQkFDWkMsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjs7O2tDQUNFLDhEQUFDUjt3QkFBSUMsT0FBTzs0QkFDVkssWUFBWTs0QkFDWkcsT0FBTzs0QkFDUEMsU0FBUzs0QkFDVEMsVUFBVTs0QkFDVkMsWUFBWTt3QkFDZDs7a0NBQUc7Ozs7OztrQ0FJSCw4REFBQ1o7d0JBQUlDLE9BQU87NEJBQUVTLFNBQVM7d0JBQU07OzswQ0FDM0IsOERBQUNWOzBFQUFjOzBDQUFrQjs7Ozs7OzBDQUNqQyw4REFBQ0E7MEVBQWM7MENBQWtCOzs7Ozs7MENBQ2pDLDhEQUFDQTswRUFBYzswQ0FBa0I7Ozs7OzswQ0FDakMsOERBQUNBOzBFQUFjOzBDQUFrQjs7Ozs7OzBDQUNqQyw4REFBQ0E7MEVBQWM7MENBQWtCOzs7Ozs7MENBQ2pDLDhEQUFDQTswRUFBYzswQ0FBa0I7Ozs7OzswQ0FDakMsOERBQUNhO2dDQUFHWixPQUFPO29DQUFFYSxRQUFRO29DQUFTUCxRQUFRO2dDQUE4Qjs7Ozs7OzswQ0FDcEUsOERBQUNQOzBFQUFjOzBDQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUt2Qyw4REFBQ0E7MERBQWM7O2tDQUNiLDhEQUFDZTt3QkFFQ0MsU0FBU2pCO3dCQUNURSxPQUFPOzRCQUNMSyxZQUFZbEIsa0JBQWtCLDhCQUE4Qjt3QkFDOUQ7a0VBSlU7OzBDQU1WLDhEQUFDNkI7OzBDQUFLOzs7Ozs7NEJBQVM7Ozs7Ozs7a0NBSWpCLDhEQUFDakI7d0JBQUlDLE9BQU87NEJBQUVpQixNQUFNO3dCQUFFOzs7Ozs7O2tDQUV0Qiw4REFBQ2xCO3dCQUFtQ0MsT0FBTzs0QkFDekNTLFNBQVM7NEJBQ1RDLFVBQVU7NEJBQ1ZMLFlBQVk7NEJBQ1phLFVBQVU7NEJBQ1ZDLFdBQVc7d0JBQ2I7a0VBTmU7a0NBT1psQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBaUJYO0dBaEd3QkQ7S0FBQUEiLCJzb3VyY2VzIjpbIkU6XFx3aW45OFxcd2luOThcXHNyY1xcYXBwXFxjb21wb25lbnRzXFxUYXNrYmFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRhc2tiYXIoKSB7XG4gIGNvbnN0IFtjdXJyZW50VGltZSwgc2V0Q3VycmVudFRpbWVdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNTdGFydE1lbnVPcGVuLCBzZXRJc1N0YXJ0TWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlVGltZSA9ICgpID0+IHtcbiAgICAgIHNldEN1cnJlbnRUaW1lKG5ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgICBtaW51dGU6ICcyLWRpZ2l0JyxcbiAgICAgICAgaG91cjEyOiBmYWxzZVxuICAgICAgfSkpO1xuICAgIH07XG5cbiAgICB1cGRhdGVUaW1lKCk7XG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCh1cGRhdGVUaW1lLCAxMDAwKTtcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCB0b2dnbGVTdGFydE1lbnUgPSAoKSA9PiB7XG4gICAgc2V0SXNTdGFydE1lbnVPcGVuKCFpc1N0YXJ0TWVudU9wZW4pO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiBTdGFydCBNZW51ICovfVxuICAgICAge2lzU3RhcnRNZW51T3BlbiAmJiAoXG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBwb3NpdGlvbjogJ2ZpeGVkJyxcbiAgICAgICAgICBib3R0b206ICcyOHB4JyxcbiAgICAgICAgICBsZWZ0OiAnMCcsXG4gICAgICAgICAgd2lkdGg6ICcyMDBweCcsXG4gICAgICAgICAgYmFja2dyb3VuZDogJ3ZhcigtLXdpbjk4LWdyYXkpJyxcbiAgICAgICAgICBib3JkZXI6ICcycHggb3V0c2V0IHZhcigtLXdpbjk4LWdyYXkpJyxcbiAgICAgICAgICB6SW5kZXg6IDEwMDFcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg5MGRlZywgdmFyKC0td2luOTgtZGFyay1ibHVlKSAwJSwgdmFyKC0td2luOTgtYmx1ZSkgMTAwJSknLFxuICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICBwYWRkaW5nOiAnOHB4JyxcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMTRweCcsXG4gICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIFdpbmRvd3MgOThcbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzRweCcgfX0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXJ0LW1lbnUtaXRlbVwiPvCfk4EgUHJvZ3JhbXM8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RhcnQtbWVudS1pdGVtXCI+8J+ThCBEb2N1bWVudHM8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RhcnQtbWVudS1pdGVtXCI+4pqZ77iPIFNldHRpbmdzPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXJ0LW1lbnUtaXRlbVwiPvCflI0gRmluZDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGFydC1tZW51LWl0ZW1cIj7inZMgSGVscDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGFydC1tZW51LWl0ZW1cIj7wn4+DIFJ1bi4uLjwvZGl2PlxuICAgICAgICAgICAgPGhyIHN0eWxlPXt7IG1hcmdpbjogJzRweCAwJywgYm9yZGVyOiAnMXB4IGluc2V0IHZhcigtLXdpbjk4LWdyYXkpJyB9fSAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGFydC1tZW51LWl0ZW1cIj7ij7sgU2h1dCBEb3duLi4uPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3aW45OC10YXNrYmFyXCI+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBjbGFzc05hbWU9XCJ3aW45OC1zdGFydC1idXR0b25cIlxuICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZVN0YXJ0TWVudX1cbiAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogaXNTdGFydE1lbnVPcGVuID8gJ3ZhcigtLXdpbjk4LWJvcmRlci1pbnNldCknIDogJ3ZhcigtLXdpbjk4LWdyYXkpJ1xuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICA8c3Bhbj7wn6qfPC9zcGFuPlxuICAgICAgICAgIFN0YXJ0XG4gICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgIDxkaXYgc3R5bGU9e3sgZmxleDogMSB9fT48L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndpbjk4LWJvcmRlci1pbnNldFwiIHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzJweCA4cHgnLFxuICAgICAgICAgIGZvbnRTaXplOiAnMTFweCcsXG4gICAgICAgICAgYmFja2dyb3VuZDogJ3ZhcigtLXdpbjk4LWdyYXkpJyxcbiAgICAgICAgICBtaW5XaWR0aDogJzYwcHgnLFxuICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcidcbiAgICAgICAgfX0+XG4gICAgICAgICAge2N1cnJlbnRUaW1lfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8c3R5bGUganN4PntgXG4gICAgICAgIC5zdGFydC1tZW51LWl0ZW0ge1xuICAgICAgICAgIHBhZGRpbmc6IDRweCA4cHg7XG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTFweDtcbiAgICAgICAgfVxuICAgICAgICAuc3RhcnQtbWVudS1pdGVtOmhvdmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS13aW45OC1oaWdobGlnaHQpO1xuICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgICAgfVxuICAgICAgYH08L3N0eWxlPlxuICAgIDwvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiVGFza2JhciIsImN1cnJlbnRUaW1lIiwic2V0Q3VycmVudFRpbWUiLCJpc1N0YXJ0TWVudU9wZW4iLCJzZXRJc1N0YXJ0TWVudU9wZW4iLCJ1cGRhdGVUaW1lIiwiRGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJob3VyMTIiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsInRvZ2dsZVN0YXJ0TWVudSIsImRpdiIsInN0eWxlIiwicG9zaXRpb24iLCJib3R0b20iLCJsZWZ0Iiwid2lkdGgiLCJiYWNrZ3JvdW5kIiwiYm9yZGVyIiwiekluZGV4IiwiY29sb3IiLCJwYWRkaW5nIiwiZm9udFNpemUiLCJmb250V2VpZ2h0IiwiaHIiLCJtYXJnaW4iLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiIsImZsZXgiLCJtaW5XaWR0aCIsInRleHRBbGlnbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Taskbar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/Winamp.tsx":
/*!***************************************!*\
  !*** ./src/app/components/Winamp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Winamp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Winamp(param) {\n    let { onClose, initialPosition } = param;\n    var _playlist_currentSong, _playlist_currentSong1;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [currentSong, setCurrentSong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [playlist, setPlaylist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load music files from public/music folder\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            const loadPlaylist = {\n                \"Winamp.useEffect.loadPlaylist\": async ()=>{\n                    try {\n                        // Try to fetch a list of files from the music directory\n                        // Since we can't directly read directory in browser, we'll use a predefined list\n                        // Users need to update this list when they add new files\n                        const musicFiles = [\n                            'sample1.mp3',\n                            'sample2.mp3',\n                            'sample3.mp3',\n                            'song1.mp3',\n                            'song2.mp3',\n                            'music.mp3',\n                            'track1.mp3',\n                            'track2.mp3'\n                        ];\n                        const availableSongs = [];\n                        for (const file of musicFiles){\n                            try {\n                                const response = await fetch(\"/music/\".concat(file), {\n                                    method: 'HEAD'\n                                });\n                                if (response.ok) {\n                                    availableSongs.push({\n                                        name: file.replace('.mp3', '').replace(/[-_]/g, ' '),\n                                        url: \"/music/\".concat(file)\n                                    });\n                                }\n                            } catch (error) {\n                            // File doesn't exist, skip it\n                            }\n                        }\n                        if (availableSongs.length === 0) {\n                            // Add default message if no songs found\n                            availableSongs.push({\n                                name: \"No songs found - Add MP3 files to /public/music/\",\n                                url: \"\"\n                            });\n                        }\n                        setPlaylist(availableSongs);\n                    } catch (error) {\n                        console.error('Error loading playlist:', error);\n                    }\n                }\n            }[\"Winamp.useEffect.loadPlaylist\"];\n            loadPlaylist();\n        }\n    }[\"Winamp.useEffect\"], []);\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Winamp.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Winamp.useEffect\"];\n            }\n        }\n    }[\"Winamp.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const updateTime = {\n                \"Winamp.useEffect.updateTime\": ()=>setCurrentTime(audio.currentTime)\n            }[\"Winamp.useEffect.updateTime\"];\n            const updateDuration = {\n                \"Winamp.useEffect.updateDuration\": ()=>setDuration(audio.duration)\n            }[\"Winamp.useEffect.updateDuration\"];\n            audio.addEventListener('timeupdate', updateTime);\n            audio.addEventListener('loadedmetadata', updateDuration);\n            return ({\n                \"Winamp.useEffect\": ()=>{\n                    audio.removeEventListener('timeupdate', updateTime);\n                    audio.removeEventListener('loadedmetadata', updateDuration);\n                }\n            })[\"Winamp.useEffect\"];\n        }\n    }[\"Winamp.useEffect\"], []);\n    const togglePlay = ()=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        if (isPlaying) {\n            audio.pause();\n        } else {\n            audio.play().catch(console.error);\n        }\n        setIsPlaying(!isPlaying);\n    };\n    const handleVolumeChange = (e)=>{\n        const newVolume = parseInt(e.target.value);\n        setVolume(newVolume);\n        if (audioRef.current) {\n            audioRef.current.volume = newVolume / 100;\n        }\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const nextSong = ()=>{\n        setCurrentSong((prev)=>(prev + 1) % playlist.length);\n        setIsPlaying(false);\n    };\n    const prevSong = ()=>{\n        setCurrentSong((prev)=>(prev - 1 + playlist.length) % playlist.length);\n        setIsPlaying(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '275px',\n            height: '116px',\n            zIndex: 100,\n            background: 'linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%)',\n            border: '2px outset #666',\n            borderRadius: '0',\n            position: 'absolute'\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                style: {\n                    background: 'linear-gradient(90deg, #ff6600 0%, #ff3300 100%)',\n                    color: 'white',\n                    padding: '2px 4px',\n                    fontSize: '11px',\n                    fontWeight: 'bold',\n                    cursor: 'move',\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    height: '14px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"\\uD83C\\uDFB5 Winamp\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        style: {\n                            background: 'none',\n                            border: 'none',\n                            color: 'white',\n                            cursor: 'pointer',\n                            fontSize: '12px',\n                            padding: '0 4px'\n                        },\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '4px',\n                    background: 'linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%)',\n                    height: 'calc(100% - 14px)',\n                    color: '#00ff00'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: (_playlist_currentSong = playlist[currentSong]) === null || _playlist_currentSong === void 0 ? void 0 : _playlist_currentSong.url,\n                        onEnded: nextSong\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#000',\n                            color: '#00ff00',\n                            fontFamily: 'monospace',\n                            fontSize: '10px',\n                            padding: '2px 4px',\n                            height: '20px',\n                            border: '1px inset #666',\n                            marginBottom: '4px',\n                            overflow: 'hidden',\n                            display: 'flex',\n                            alignItems: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    flex: 1,\n                                    overflow: 'hidden',\n                                    whiteSpace: 'nowrap'\n                                },\n                                children: ((_playlist_currentSong1 = playlist[currentSong]) === null || _playlist_currentSong1 === void 0 ? void 0 : _playlist_currentSong1.name) || 'Winamp v2.95'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    minWidth: '50px',\n                                    textAlign: 'right'\n                                },\n                                children: formatTime(currentTime)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '2px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '2px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '1px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: prevSong,\n                                                style: {\n                                                    width: '23px',\n                                                    height: '18px',\n                                                    fontSize: '8px',\n                                                    background: 'linear-gradient(135deg, #666 0%, #333 100%)',\n                                                    border: '1px outset #666',\n                                                    color: '#ccc'\n                                                },\n                                                children: \"⏮\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: togglePlay,\n                                                style: {\n                                                    width: '23px',\n                                                    height: '18px',\n                                                    fontSize: '8px',\n                                                    background: 'linear-gradient(135deg, #666 0%, #333 100%)',\n                                                    border: '1px outset #666',\n                                                    color: '#ccc'\n                                                },\n                                                children: isPlaying ? '⏸' : '▶'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setIsPlaying(false);\n                                                    if (audioRef.current) audioRef.current.pause();\n                                                },\n                                                style: {\n                                                    width: '23px',\n                                                    height: '18px',\n                                                    fontSize: '8px',\n                                                    background: 'linear-gradient(135deg, #666 0%, #333 100%)',\n                                                    border: '1px outset #666',\n                                                    color: '#ccc'\n                                                },\n                                                children: \"⏹\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: nextSong,\n                                                style: {\n                                                    width: '23px',\n                                                    height: '18px',\n                                                    fontSize: '8px',\n                                                    background: 'linear-gradient(135deg, #666 0%, #333 100%)',\n                                                    border: '1px outset #666',\n                                                    color: '#ccc'\n                                                },\n                                                children: \"⏭\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '2px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '8px',\n                                                    color: '#ccc',\n                                                    minWidth: '20px'\n                                                },\n                                                children: \"Vol\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                value: volume,\n                                                onChange: handleVolumeChange,\n                                                style: {\n                                                    width: '60px',\n                                                    height: '10px',\n                                                    background: '#333'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    flex: 1,\n                                    background: '#000',\n                                    border: '1px inset #666',\n                                    height: '40px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    fontSize: '8px',\n                                    color: '#00ff00'\n                                },\n                                children: isPlaying ? '♪♫♪♫♪' : '- - -'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n_s(Winamp, \"cf+PyMWo0aSzwZLtrM9qpXjbLZc=\");\n_c = Winamp;\nvar _c;\n$RefreshReg$(_c, \"Winamp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Winamp.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/DesktopIcon */ \"(app-pages-browser)/./src/app/components/DesktopIcon.tsx\");\n/* harmony import */ var _components_ChatWindow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ChatWindow */ \"(app-pages-browser)/./src/app/components/ChatWindow.tsx\");\n/* harmony import */ var _components_Taskbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Taskbar */ \"(app-pages-browser)/./src/app/components/Taskbar.tsx\");\n/* harmony import */ var _components_Winamp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/Winamp */ \"(app-pages-browser)/./src/app/components/Winamp.tsx\");\n/* harmony import */ var _components_Solitaire__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/Solitaire */ \"(app-pages-browser)/./src/app/components/Solitaire.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isWinampOpen, setIsWinampOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSolitaireOpen, setIsSolitaireOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleIconDoubleClick = ()=>{\n        setIsChatOpen(true);\n    };\n    const handleCloseChat = ()=>{\n        setIsChatOpen(false);\n    };\n    const handleCloseWinamp = ()=>{\n        setIsWinampOpen(false);\n    };\n    const handleCloseSolitaire = ()=>{\n        setIsSolitaireOpen(false);\n    };\n    const handleDesktopClick = ()=>{\n    // This will be passed to DesktopIcon to deselect all icons\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"win98-desktop\",\n        onClick: handleDesktopClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCAC\",\n                label: \"Chat with Sheila\",\n                position: {\n                    x: 50,\n                    y: 50\n                },\n                onDoubleClick: handleIconDoubleClick\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDDA5️\",\n                label: \"My Computer\",\n                position: {\n                    x: 50,\n                    y: 140\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCC2\",\n                label: \"My Documents\",\n                position: {\n                    x: 50,\n                    y: 230\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDDD1️\",\n                label: \"Recycle Bin\",\n                position: {\n                    x: 50,\n                    y: 320\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDF0D\",\n                label: \"Internet Explorer\",\n                position: {\n                    x: 50,\n                    y: 410\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"✉️\",\n                label: \"Outlook Express\",\n                position: {\n                    x: 150,\n                    y: 50\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDFB5\",\n                label: \"Winamp\",\n                position: {\n                    x: 150,\n                    y: 140\n                },\n                onDoubleClick: ()=>setIsWinampOpen(true)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDCCF\",\n                label: \"Solitaire\",\n                position: {\n                    x: 150,\n                    y: 230\n                },\n                onDoubleClick: ()=>setIsSolitaireOpen(true)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDD8C️\",\n                label: \"Paint\",\n                position: {\n                    x: 150,\n                    y: 320\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCC4\",\n                label: \"Notepad\",\n                position: {\n                    x: 150,\n                    y: 410\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"⚙️\",\n                label: \"Control Panel\",\n                position: {\n                    x: 250,\n                    y: 50\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCBE\",\n                label: \"3\\xbd Floppy (A:)\",\n                position: {\n                    x: 250,\n                    y: 140\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCBF\",\n                label: \"CD-ROM (D:)\",\n                position: {\n                    x: 250,\n                    y: 230\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            isChatOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                title: \"Chat with Sheila\",\n                onClose: handleCloseChat,\n                initialPosition: {\n                    x: 200,\n                    y: 100\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this),\n            isWinampOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Winamp__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onClose: handleCloseWinamp,\n                initialPosition: {\n                    x: 300,\n                    y: 150\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this),\n            isSolitaireOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Solitaire__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClose: handleCloseSolitaire,\n                initialPosition: {\n                    x: 250,\n                    y: 80\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Taskbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"OYANax6QzOWMqYobFxeNIHbivy4=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cwin98%5C%5Cwin98%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);