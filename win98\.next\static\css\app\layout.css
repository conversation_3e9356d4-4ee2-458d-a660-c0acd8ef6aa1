/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .flex {
    display: flex;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
:root {
  --win98-gray: #c0c0c0;
  --win98-dark-gray: #808080;
  --win98-light-gray: #dfdfdf;
  --win98-white: #ffffff;
  --win98-black: #000000;
  --win98-blue: #0000ff;
  --win98-dark-blue: #000080;
  --win98-desktop: #008080;
  --win98-highlight: #316ac5;
  --win98-shadow-dark: #404040;
  --win98-shadow-light: #ffffff;
}
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
body {
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  background: var(--win98-desktop);
  overflow: hidden;
  height: 100vh;
  cursor: default;
  user-select: none;
}
.win98-border {
  border: 2px solid;
  border-color: var(--win98-light-gray) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-light-gray);
}
.win98-border-inset {
  border: 2px solid;
  border-color: var(--win98-shadow-dark) var(--win98-light-gray) var(--win98-light-gray) var(--win98-shadow-dark);
}
.win98-border-raised {
  border: 1px solid;
  border-color: var(--win98-white) var(--win98-dark-gray) var(--win98-dark-gray) var(--win98-white);
}
.win98-button {
  background: var(--win98-gray);
  border: 2px solid;
  border-color: var(--win98-white) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-white);
  padding: 2px 8px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  cursor: pointer;
  outline: none;
}
.win98-button:active {
  border-color: var(--win98-shadow-dark) var(--win98-white) var(--win98-white) var(--win98-shadow-dark);
  padding: 3px 7px 1px 9px;
}
.win98-button:focus {
  outline: 1px dotted var(--win98-black);
  outline-offset: -4px;
}
.win98-window {
  background: var(--win98-gray);
  border: 2px solid;
  border-color: var(--win98-light-gray) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-light-gray);
  position: absolute;
  min-width: 200px;
  min-height: 100px;
}
.win98-title-bar {
  background: linear-gradient(90deg, var(--win98-dark-blue) 0%, var(--win98-blue) 100%);
  color: var(--win98-white);
  padding: 2px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 18px;
  cursor: move;
}
.win98-title-bar.inactive {
  background: var(--win98-dark-gray);
}
.win98-title-text {
  padding-left: 4px;
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
}
.win98-window-controls {
  display: flex;
  gap: 2px;
  padding-right: 2px;
}
.win98-control-button {
  width: 16px;
  height: 14px;
  background: var(--win98-gray);
  border: 1px solid;
  border-color: var(--win98-white) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-white);
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--win98-black);
}
.win98-control-button:active {
  border-color: var(--win98-shadow-dark) var(--win98-white) var(--win98-white) var(--win98-shadow-dark);
}
.win98-desktop {
  width: 100vw;
  height: 100vh;
  background: var(--win98-desktop);
  position: relative;
  overflow: hidden;
}
.win98-taskbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 28px;
  background: var(--win98-gray);
  border-top: 1px solid var(--win98-light-gray);
  display: flex;
  align-items: center;
  padding: 2px;
  z-index: 1000;
}
.win98-start-button {
  background: var(--win98-gray);
  border: 2px solid;
  border-color: var(--win98-white) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-white);
  padding: 2px 8px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  height: 22px;
}
.win98-start-button:active {
  border-color: var(--win98-shadow-dark) var(--win98-white) var(--win98-white) var(--win98-shadow-dark);
}
.win98-desktop-icon {
  position: absolute;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 6px;
  border: 1px solid transparent;
}
.win98-desktop-icon:hover {
  background: rgba(255, 255, 255, 0.1);
}
.win98-desktop-icon.selected {
  background: var(--win98-highlight);
  border: 1px dotted var(--win98-white);
}
.win98-icon-image {
  width: 48px;
  height: 48px;
  background: transparent;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  filter: drop-shadow(1px 1px 1px rgba(0,0,0,0.5));
}
.win98-icon-label {
  color: var(--win98-white);
  font-size: 11px;
  text-align: center;
  text-shadow: 1px 1px 1px var(--win98-black);
  word-wrap: break-word;
  max-width: 75px;
  line-height: 1.2;
}
.win98-chat-window {
  width: 400px;
  height: 500px;
  display: flex;
  flex-direction: column;
}
.win98-chat-content {
  flex: 1;
  background: var(--win98-white);
  border: 2px solid;
  border-color: var(--win98-shadow-dark) var(--win98-light-gray) var(--win98-light-gray) var(--win98-shadow-dark);
  margin: 2px;
  padding: 8px;
  overflow-y: auto;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
}
.win98-chat-input-area {
  background: var(--win98-gray);
  padding: 4px;
  border-top: 1px solid var(--win98-shadow-dark);
  display: flex;
  gap: 4px;
}
.win98-chat-input {
  flex: 1;
  border: 2px solid;
  border-color: var(--win98-shadow-dark) var(--win98-light-gray) var(--win98-light-gray) var(--win98-shadow-dark);
  padding: 2px 4px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  background: var(--win98-white);
}
.win98-chat-input:focus {
  outline: none;
}
.chat-message {
  margin-bottom: 8px;
  padding: 4px;
}
.chat-message.user {
  text-align: right;
  background: #e6f3ff;
  border: 1px solid #b3d9ff;
}
.chat-message.assistant {
  text-align: left;
  background: #f0f0f0;
  border: 1px solid #d0d0d0;
}
.message-sender {
  font-weight: bold;
  margin-bottom: 2px;
  font-size: 10px;
}
.message-content {
  line-height: 1.3;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-border-style: solid;
    }
  }
}

