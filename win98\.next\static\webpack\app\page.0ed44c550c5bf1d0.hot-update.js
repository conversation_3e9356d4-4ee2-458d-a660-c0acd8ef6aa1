"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/DesktopIcon.tsx":
/*!********************************************!*\
  !*** ./src/app/components/DesktopIcon.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesktopIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DesktopIcon(param) {\n    let { icon, label, position, onDoubleClick } = param;\n    _s();\n    const [isSelected, setIsSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clickCount, setClickCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleClick = (e)=>{\n        e.stopPropagation(); // Prevent desktop click\n        setIsSelected(true);\n        setClickCount((prev)=>prev + 1);\n        // Reset click count after 300ms if no second click\n        setTimeout(()=>{\n            if (clickCount === 0) {\n                setClickCount(0);\n            }\n        }, 300);\n        // Handle double click\n        if (clickCount === 1) {\n            onDoubleClick();\n            setClickCount(0);\n        }\n    };\n    // Listen for clicks outside to deselect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DesktopIcon.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"DesktopIcon.useEffect.handleClickOutside\": ()=>{\n                    setIsSelected(false);\n                }\n            }[\"DesktopIcon.useEffect.handleClickOutside\"];\n            document.addEventListener('click', handleClickOutside);\n            return ({\n                \"DesktopIcon.useEffect\": ()=>{\n                    document.removeEventListener('click', handleClickOutside);\n                }\n            })[\"DesktopIcon.useEffect\"];\n        }\n    }[\"DesktopIcon.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"win98-desktop-icon \".concat(isSelected ? 'selected' : ''),\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\")\n        },\n        onClick: handleClick,\n        onBlur: ()=>setIsSelected(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"win98-icon-image\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"win98-icon-label\",\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(DesktopIcon, \"TeF6O8Dwy0cOoLnA7CxZcXBh7Wc=\");\n_c = DesktopIcon;\nvar _c;\n$RefreshReg$(_c, \"DesktopIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/DesktopIcon.tsx\n"));

/***/ })

});