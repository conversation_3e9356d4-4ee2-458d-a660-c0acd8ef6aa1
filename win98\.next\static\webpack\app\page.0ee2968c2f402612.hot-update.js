"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/Winamp.tsx":
/*!***************************************!*\
  !*** ./src/app/components/Winamp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Winamp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Winamp(param) {\n    let { onClose, initialPosition } = param;\n    var _playlist_currentSong, _playlist_currentSong1;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [currentSong, setCurrentSong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [playlist, setPlaylist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load music files from public/music folder\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            const loadPlaylist = {\n                \"Winamp.useEffect.loadPlaylist\": async ()=>{\n                    try {\n                        // Try to fetch a list of files from the music directory\n                        // Since we can't directly read directory in browser, we'll use a predefined list\n                        // Users need to update this list when they add new files\n                        const musicFiles = [\n                            'sample1.mp3',\n                            'sample2.mp3',\n                            'sample3.mp3',\n                            'song1.mp3',\n                            'song2.mp3',\n                            'music.mp3',\n                            'track1.mp3',\n                            'track2.mp3'\n                        ];\n                        const availableSongs = [];\n                        for (const file of musicFiles){\n                            try {\n                                const response = await fetch(\"/music/\".concat(file), {\n                                    method: 'HEAD'\n                                });\n                                if (response.ok) {\n                                    availableSongs.push({\n                                        name: file.replace('.mp3', '').replace(/[-_]/g, ' '),\n                                        url: \"/music/\".concat(file)\n                                    });\n                                }\n                            } catch (error) {\n                            // File doesn't exist, skip it\n                            }\n                        }\n                        if (availableSongs.length === 0) {\n                            // Add default message if no songs found\n                            availableSongs.push({\n                                name: \"No songs found - Add MP3 files to /public/music/\",\n                                url: \"\"\n                            });\n                        }\n                        setPlaylist(availableSongs);\n                    } catch (error) {\n                        console.error('Error loading playlist:', error);\n                    }\n                }\n            }[\"Winamp.useEffect.loadPlaylist\"];\n            loadPlaylist();\n        }\n    }[\"Winamp.useEffect\"], []);\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Winamp.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Winamp.useEffect\"];\n            }\n        }\n    }[\"Winamp.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const updateTime = {\n                \"Winamp.useEffect.updateTime\": ()=>setCurrentTime(audio.currentTime)\n            }[\"Winamp.useEffect.updateTime\"];\n            const updateDuration = {\n                \"Winamp.useEffect.updateDuration\": ()=>setDuration(audio.duration)\n            }[\"Winamp.useEffect.updateDuration\"];\n            audio.addEventListener('timeupdate', updateTime);\n            audio.addEventListener('loadedmetadata', updateDuration);\n            return ({\n                \"Winamp.useEffect\": ()=>{\n                    audio.removeEventListener('timeupdate', updateTime);\n                    audio.removeEventListener('loadedmetadata', updateDuration);\n                }\n            })[\"Winamp.useEffect\"];\n        }\n    }[\"Winamp.useEffect\"], []);\n    const togglePlay = ()=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        if (isPlaying) {\n            audio.pause();\n        } else {\n            audio.play().catch(console.error);\n        }\n        setIsPlaying(!isPlaying);\n    };\n    const handleVolumeChange = (e)=>{\n        const newVolume = parseInt(e.target.value);\n        setVolume(newVolume);\n        if (audioRef.current) {\n            audioRef.current.volume = newVolume / 100;\n        }\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const nextSong = ()=>{\n        setCurrentSong((prev)=>(prev + 1) % playlist.length);\n        setIsPlaying(false);\n    };\n    const prevSong = ()=>{\n        setCurrentSong((prev)=>(prev - 1 + playlist.length) % playlist.length);\n        setIsPlaying(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        className: \"win98-window\",\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '300px',\n            height: '200px',\n            zIndex: 100\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                className: \"win98-title-bar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-title-text\",\n                        children: \"\\uD83C\\uDFB5 Winamp\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-window-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"_\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"□\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '8px',\n                    background: 'var(--win98-gray)',\n                    height: 'calc(100% - 18px)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: (_playlist_currentSong = playlist[currentSong]) === null || _playlist_currentSong === void 0 ? void 0 : _playlist_currentSong.url,\n                        onEnded: nextSong\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-border-inset\",\n                        style: {\n                            padding: '4px',\n                            background: '#000',\n                            color: '#00ff00',\n                            fontFamily: 'monospace',\n                            fontSize: '12px',\n                            marginBottom: '8px',\n                            height: '40px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: ((_playlist_currentSong1 = playlist[currentSong]) === null || _playlist_currentSong1 === void 0 ? void 0 : _playlist_currentSong1.name) || 'No song loaded'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    formatTime(currentTime),\n                                    \" / \",\n                                    formatTime(duration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '4px',\n                            marginBottom: '8px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: prevSong,\n                                children: \"⏮️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: togglePlay,\n                                children: isPlaying ? '⏸️' : '▶️'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: nextSong,\n                                children: \"⏭️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                children: \"⏹️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '10px'\n                                },\n                                children: \"Vol:\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"range\",\n                                min: \"0\",\n                                max: \"100\",\n                                value: volume,\n                                onChange: handleVolumeChange,\n                                style: {\n                                    flex: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '10px',\n                                    minWidth: '30px'\n                                },\n                                children: [\n                                    volume,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '9px',\n                            marginTop: '8px',\n                            color: 'var(--win98-shadow-dark)',\n                            textAlign: 'center'\n                        },\n                        children: \"Add MP3 files to /public/music/ folder\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n_s(Winamp, \"cf+PyMWo0aSzwZLtrM9qpXjbLZc=\");\n_c = Winamp;\nvar _c;\n$RefreshReg$(_c, \"Winamp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Winamp.tsx\n"));

/***/ })

});