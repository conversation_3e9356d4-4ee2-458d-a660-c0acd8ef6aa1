"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/DesktopIcon.tsx":
/*!********************************************!*\
  !*** ./src/app/components/DesktopIcon.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DesktopIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DesktopIcon(param) {\n    let { icon, label, position, onDoubleClick } = param;\n    _s();\n    const [isSelected, setIsSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clickCount, setClickCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleClick = (e)=>{\n        e.stopPropagation(); // Prevent desktop click\n        setIsSelected(true);\n        setClickCount((prev)=>prev + 1);\n        // Reset click count after 300ms if no second click\n        setTimeout(()=>{\n            if (clickCount === 0) {\n                setClickCount(0);\n            }\n        }, 300);\n        // Handle double click\n        if (clickCount === 1) {\n            onDoubleClick();\n            setClickCount(0);\n        }\n    };\n    // Listen for clicks outside to deselect\n    useEffect({\n        \"DesktopIcon.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"DesktopIcon.useEffect.handleClickOutside\": ()=>{\n                    setIsSelected(false);\n                }\n            }[\"DesktopIcon.useEffect.handleClickOutside\"];\n            document.addEventListener('click', handleClickOutside);\n            return ({\n                \"DesktopIcon.useEffect\": ()=>{\n                    document.removeEventListener('click', handleClickOutside);\n                }\n            })[\"DesktopIcon.useEffect\"];\n        }\n    }[\"DesktopIcon.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"win98-desktop-icon \".concat(isSelected ? 'selected' : ''),\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\")\n        },\n        onClick: handleClick,\n        onBlur: ()=>setIsSelected(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"win98-icon-image\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"win98-icon-label\",\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\DesktopIcon.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(DesktopIcon, \"TeF6O8Dwy0cOoLnA7CxZcXBh7Wc=\");\n_c = DesktopIcon;\nvar _c;\n$RefreshReg$(_c, \"DesktopIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/DesktopIcon.tsx\n"));

/***/ })

});