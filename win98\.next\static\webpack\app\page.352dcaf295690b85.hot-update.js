"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/Solitaire.tsx":
/*!******************************************!*\
  !*** ./src/app/components/Solitaire.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Solitaire)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Solitaire(param) {\n    let { onClose, initialPosition } = param;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [deck, setDeck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const suits = [\n        '♠',\n        '♥',\n        '♦',\n        '♣'\n    ];\n    const values = [\n        'A',\n        '2',\n        '3',\n        '4',\n        '5',\n        '6',\n        '7',\n        '8',\n        '9',\n        '10',\n        'J',\n        'Q',\n        'K'\n    ];\n    const createDeck = ()=>{\n        const newDeck = [];\n        suits.forEach((suit)=>{\n            values.forEach((value)=>{\n                newDeck.push({\n                    suit,\n                    value,\n                    color: suit === '♥' || suit === '♦' ? 'red' : 'black',\n                    faceUp: false\n                });\n            });\n        });\n        return shuffleDeck(newDeck);\n    };\n    const shuffleDeck = (deck)=>{\n        const shuffled = [\n            ...deck\n        ];\n        for(let i = shuffled.length - 1; i > 0; i--){\n            const j = Math.floor(Math.random() * (i + 1));\n            [shuffled[i], shuffled[j]] = [\n                shuffled[j],\n                shuffled[i]\n            ];\n        }\n        return shuffled;\n    };\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Solitaire.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Solitaire.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Solitaire.useEffect\"];\n            }\n        }\n    }[\"Solitaire.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    const startNewGame = ()=>{\n        setDeck(createDeck());\n        setGameStarted(true);\n    };\n    const CardComponent = (param)=>{\n        let { card, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"solitaire-card\",\n            onClick: onClick,\n            style: {\n                width: '50px',\n                height: '70px',\n                border: '1px solid var(--win98-black)',\n                background: card.faceUp ? 'white' : 'var(--win98-blue)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                color: card.faceUp ? card.color : 'white',\n                cursor: 'pointer',\n                margin: '2px'\n            },\n            children: card.faceUp ? \"\".concat(card.value).concat(card.suit) : '🂠'\n        }, void 0, false, {\n            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n            lineNumber: 97,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        className: \"win98-window\",\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '500px',\n            height: '400px',\n            zIndex: 100\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                className: \"win98-title-bar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-title-text\",\n                        children: \"\\uD83C\\uDCCF Solitaire\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-window-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"_\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"□\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '8px',\n                    background: 'var(--win98-gray)',\n                    height: 'calc(100% - 18px)',\n                    overflow: 'auto'\n                },\n                children: !gameStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        height: '100%'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                marginBottom: '20px'\n                            },\n                            children: \"Windows Solitaire\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"win98-button\",\n                            onClick: startNewGame,\n                            children: \"New Game\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '20px',\n                                fontSize: '11px',\n                                textAlign: 'center',\n                                color: 'var(--win98-shadow-dark)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Classic Klondike Solitaire\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Click cards to flip them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Drag cards to move them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"win98-button\",\n                                    onClick: startNewGame,\n                                    children: \"New Game\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginLeft: '20px',\n                                        fontSize: '11px'\n                                    },\n                                    children: [\n                                        \"Cards: \",\n                                        deck.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'var(--win98-desktop)',\n                                minHeight: '300px',\n                                border: '2px inset var(--win98-gray)',\n                                padding: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '10px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '5px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px'\n                                                        },\n                                                        children: \"Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px'\n                                                        },\n                                                        children: \"Waste\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '5px'\n                                            },\n                                            children: suits.map((suit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"win98-border-inset\",\n                                                    style: {\n                                                        width: '54px',\n                                                        height: '74px',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: suit\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '10px',\n                                        justifyContent: 'center'\n                                    },\n                                    children: [\n                                        1,\n                                        2,\n                                        3,\n                                        4,\n                                        5,\n                                        6,\n                                        7\n                                    ].map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column'\n                                            },\n                                            children: deck.slice(0, column).map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardComponent, {\n                                                    card: {\n                                                        ...card,\n                                                        faceUp: index === column - 1\n                                                    },\n                                                    onClick: ()=>{\n                                                        // Simple card flip logic\n                                                        const newDeck = [\n                                                            ...deck\n                                                        ];\n                                                        newDeck[index].faceUp = !newDeck[index].faceUp;\n                                                        setDeck(newDeck);\n                                                    }\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, column, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(Solitaire, \"ZZk1HU/jLkEyAN7l88wc6CnreMQ=\");\n_c = Solitaire;\nvar _c;\n$RefreshReg$(_c, \"Solitaire\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29tcG9uZW50cy9Tb2xpdGFpcmUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVvRDtBQWNyQyxTQUFTRyxVQUFVLEtBQTRDO1FBQTVDLEVBQUVDLE9BQU8sRUFBRUMsZUFBZSxFQUFrQixHQUE1Qzs7SUFDaEMsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdQLCtDQUFRQSxDQUFDSztJQUN6QyxNQUFNLENBQUNHLFlBQVlDLGNBQWMsR0FBR1QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDVSxZQUFZQyxjQUFjLEdBQUdYLCtDQUFRQSxDQUFDO1FBQUVZLEdBQUc7UUFBR0MsR0FBRztJQUFFO0lBQzFELE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHZiwrQ0FBUUEsQ0FBUyxFQUFFO0lBQzNDLE1BQU0sQ0FBQ2dCLGFBQWFDLGVBQWUsR0FBR2pCLCtDQUFRQSxDQUFDO0lBRS9DLE1BQU1rQixZQUFZakIsNkNBQU1BLENBQWlCO0lBQ3pDLE1BQU1rQixjQUFjbEIsNkNBQU1BLENBQWlCO0lBRTNDLE1BQU1tQixRQUFzQztRQUFDO1FBQUs7UUFBSztRQUFLO0tBQUk7SUFDaEUsTUFBTUMsU0FBUztRQUFDO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQU07UUFBSztRQUFLO0tBQUk7SUFFakYsTUFBTUMsYUFBYTtRQUNqQixNQUFNQyxVQUFrQixFQUFFO1FBQzFCSCxNQUFNSSxPQUFPLENBQUNDLENBQUFBO1lBQ1pKLE9BQU9HLE9BQU8sQ0FBQ0UsQ0FBQUE7Z0JBQ2JILFFBQVFJLElBQUksQ0FBQztvQkFDWEY7b0JBQ0FDO29CQUNBRSxPQUFPSCxTQUFTLE9BQU9BLFNBQVMsTUFBTSxRQUFRO29CQUM5Q0ksUUFBUTtnQkFDVjtZQUNGO1FBQ0Y7UUFDQSxPQUFPQyxZQUFZUDtJQUNyQjtJQUVBLE1BQU1PLGNBQWMsQ0FBQ2hCO1FBQ25CLE1BQU1pQixXQUFXO2VBQUlqQjtTQUFLO1FBQzFCLElBQUssSUFBSWtCLElBQUlELFNBQVNFLE1BQU0sR0FBRyxHQUFHRCxJQUFJLEdBQUdBLElBQUs7WUFDNUMsTUFBTUUsSUFBSUMsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQU1MLENBQUFBLElBQUk7WUFDMUMsQ0FBQ0QsUUFBUSxDQUFDQyxFQUFFLEVBQUVELFFBQVEsQ0FBQ0csRUFBRSxDQUFDLEdBQUc7Z0JBQUNILFFBQVEsQ0FBQ0csRUFBRTtnQkFBRUgsUUFBUSxDQUFDQyxFQUFFO2FBQUM7UUFDekQ7UUFDQSxPQUFPRDtJQUNUO0lBRUEsTUFBTU8sa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUlwQixZQUFZcUIsT0FBTyxJQUFJckIsWUFBWXFCLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixFQUFFRyxNQUFNLEdBQVc7Z0JBRTVEeEI7WUFEYlQsY0FBYztZQUNkLE1BQU1rQyxRQUFPekIscUJBQUFBLFVBQVVzQixPQUFPLGNBQWpCdEIseUNBQUFBLG1CQUFtQjBCLHFCQUFxQjtZQUNyRCxJQUFJRCxNQUFNO2dCQUNSaEMsY0FBYztvQkFDWkMsR0FBRzJCLEVBQUVNLE9BQU8sR0FBR0YsS0FBS0csSUFBSTtvQkFDeEJqQyxHQUFHMEIsRUFBRVEsT0FBTyxHQUFHSixLQUFLSyxHQUFHO2dCQUN6QjtZQUNGO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLGtCQUFrQixDQUFDVjtRQUN2QixJQUFJL0IsWUFBWTtZQUNkRCxZQUFZO2dCQUNWSyxHQUFHMkIsRUFBRU0sT0FBTyxHQUFHbkMsV0FBV0UsQ0FBQztnQkFDM0JDLEdBQUcwQixFQUFFUSxPQUFPLEdBQUdyQyxXQUFXRyxDQUFDO1lBQzdCO1FBQ0Y7SUFDRjtJQUVBLE1BQU1xQyxnQkFBZ0I7UUFDcEJ6QyxjQUFjO0lBQ2hCO0lBRUFQLGdEQUFTQTsrQkFBQztZQUNSLElBQUlNLFlBQVk7Z0JBQ2QyQyxTQUFTQyxnQkFBZ0IsQ0FBQyxhQUFhSDtnQkFDdkNFLFNBQVNDLGdCQUFnQixDQUFDLFdBQVdGO2dCQUNyQzsyQ0FBTzt3QkFDTEMsU0FBU0UsbUJBQW1CLENBQUMsYUFBYUo7d0JBQzFDRSxTQUFTRSxtQkFBbUIsQ0FBQyxXQUFXSDtvQkFDMUM7O1lBQ0Y7UUFDRjs4QkFBRztRQUFDMUM7UUFBWUU7S0FBVztJQUUzQixNQUFNNEMsZUFBZTtRQUNuQnZDLFFBQVFPO1FBQ1JMLGVBQWU7SUFDakI7SUFFQSxNQUFNc0MsZ0JBQWdCO1lBQUMsRUFBRUMsSUFBSSxFQUFFQyxPQUFPLEVBQXdDOzZCQUM1RSw4REFBQ0M7WUFDQ0MsV0FBVTtZQUNWRixTQUFTQTtZQUNURyxPQUFPO2dCQUNMQyxPQUFPO2dCQUNQQyxRQUFRO2dCQUNSQyxRQUFRO2dCQUNSQyxZQUFZUixLQUFLM0IsTUFBTSxHQUFHLFVBQVU7Z0JBQ3BDb0MsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsZ0JBQWdCO2dCQUNoQkMsVUFBVTtnQkFDVnhDLE9BQU80QixLQUFLM0IsTUFBTSxHQUFHMkIsS0FBSzVCLEtBQUssR0FBRztnQkFDbEN5QyxRQUFRO2dCQUNSQyxRQUFRO1lBQ1Y7c0JBRUNkLEtBQUszQixNQUFNLEdBQUcsR0FBZ0IyQixPQUFiQSxLQUFLOUIsS0FBSyxFQUFhLE9BQVY4QixLQUFLL0IsSUFBSSxJQUFLOzs7Ozs7O0lBSWpELHFCQUNFLDhEQUFDaUM7UUFDQ2EsS0FBS3JEO1FBQ0x5QyxXQUFVO1FBQ1ZDLE9BQU87WUFDTGQsTUFBTSxHQUFjLE9BQVh4QyxTQUFTTSxDQUFDLEVBQUM7WUFDcEJvQyxLQUFLLEdBQWMsT0FBWDFDLFNBQVNPLENBQUMsRUFBQztZQUNuQmdELE9BQU87WUFDUEMsUUFBUTtZQUNSVSxRQUFRO1FBQ1Y7UUFDQUMsYUFBYW5DOzswQkFFYiw4REFBQ29CO2dCQUFJYSxLQUFLcEQ7Z0JBQWF3QyxXQUFVOztrQ0FDL0IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUFtQjs7Ozs7O2tDQUNsQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDZTtnQ0FBT2YsV0FBVTswQ0FBdUI7Ozs7OzswQ0FDekMsOERBQUNlO2dDQUFPZixXQUFVOzBDQUF1Qjs7Ozs7OzBDQUN6Qyw4REFBQ2U7Z0NBQU9mLFdBQVU7Z0NBQXVCRixTQUFTckQ7MENBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFJL0QsOERBQUNzRDtnQkFBSUUsT0FBTztvQkFDVmUsU0FBUztvQkFDVFgsWUFBWTtvQkFDWkYsUUFBUTtvQkFDUmMsVUFBVTtnQkFDWjswQkFDRyxDQUFDNUQsNEJBQ0EsOERBQUMwQztvQkFBSUUsT0FBTzt3QkFDVkssU0FBUzt3QkFDVFksZUFBZTt3QkFDZlgsWUFBWTt3QkFDWkMsZ0JBQWdCO3dCQUNoQkwsUUFBUTtvQkFDVjs7c0NBQ0UsOERBQUNnQjs0QkFBR2xCLE9BQU87Z0NBQUVtQixjQUFjOzRCQUFPO3NDQUFHOzs7Ozs7c0NBQ3JDLDhEQUFDTDs0QkFBT2YsV0FBVTs0QkFBZUYsU0FBU0g7c0NBQWM7Ozs7OztzQ0FHeEQsOERBQUNJOzRCQUFJRSxPQUFPO2dDQUNWb0IsV0FBVztnQ0FDWFosVUFBVTtnQ0FDVmEsV0FBVztnQ0FDWHJELE9BQU87NEJBQ1Q7OzhDQUNFLDhEQUFDc0Q7OENBQUU7Ozs7Ozs4Q0FDSCw4REFBQ0E7OENBQUU7Ozs7Ozs4Q0FDSCw4REFBQ0E7OENBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O3lDQUlQLDhEQUFDeEI7O3NDQUVDLDhEQUFDQTs0QkFBSUUsT0FBTztnQ0FBRW1CLGNBQWM7NEJBQU87OzhDQUNqQyw4REFBQ0w7b0NBQU9mLFdBQVU7b0NBQWVGLFNBQVNIOzhDQUFjOzs7Ozs7OENBR3hELDhEQUFDNkI7b0NBQUt2QixPQUFPO3dDQUFFd0IsWUFBWTt3Q0FBUWhCLFVBQVU7b0NBQU87O3dDQUFHO3dDQUM3Q3RELEtBQUttQixNQUFNOzs7Ozs7Ozs7Ozs7O3NDQUt2Qiw4REFBQ3lCOzRCQUFJRSxPQUFPO2dDQUNWSSxZQUFZO2dDQUNacUIsV0FBVztnQ0FDWHRCLFFBQVE7Z0NBQ1JZLFNBQVM7NEJBQ1g7OzhDQUVFLDhEQUFDakI7b0NBQUlFLE9BQU87d0NBQUVLLFNBQVM7d0NBQVFFLGdCQUFnQjt3Q0FBaUJZLGNBQWM7b0NBQU87O3NEQUNuRiw4REFBQ3JCOzRDQUFJRSxPQUFPO2dEQUFFSyxTQUFTO2dEQUFRcUIsS0FBSzs0Q0FBTztzREFFekMsNEVBQUM1QjtnREFBSUUsT0FBTztvREFBRUssU0FBUztvREFBUXFCLEtBQUs7Z0RBQU07O2tFQUN4Qyw4REFBQzVCO3dEQUFJQyxXQUFVO3dEQUFxQkMsT0FBTzs0REFDekNDLE9BQU87NERBQ1BDLFFBQVE7NERBQ1JHLFNBQVM7NERBQ1RDLFlBQVk7NERBQ1pDLGdCQUFnQjs0REFDaEJDLFVBQVU7d0RBQ1o7a0VBQUc7Ozs7OztrRUFHSCw4REFBQ1Y7d0RBQUlDLFdBQVU7d0RBQXFCQyxPQUFPOzREQUN6Q0MsT0FBTzs0REFDUEMsUUFBUTs0REFDUkcsU0FBUzs0REFDVEMsWUFBWTs0REFDWkMsZ0JBQWdCOzREQUNoQkMsVUFBVTt3REFDWjtrRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBT1AsOERBQUNWOzRDQUFJRSxPQUFPO2dEQUFFSyxTQUFTO2dEQUFRcUIsS0FBSzs0Q0FBTTtzREFDdkNsRSxNQUFNbUUsR0FBRyxDQUFDLENBQUM5RCxNQUFNK0Qsc0JBQ2hCLDhEQUFDOUI7b0RBQWdCQyxXQUFVO29EQUFxQkMsT0FBTzt3REFDckRDLE9BQU87d0RBQ1BDLFFBQVE7d0RBQ1JHLFNBQVM7d0RBQ1RDLFlBQVk7d0RBQ1pDLGdCQUFnQjt3REFDaEJDLFVBQVU7b0RBQ1o7OERBQ0czQzttREFSTytEOzs7Ozs7Ozs7Ozs7Ozs7OzhDQWVoQiw4REFBQzlCO29DQUFJRSxPQUFPO3dDQUFFSyxTQUFTO3dDQUFRcUIsS0FBSzt3Q0FBUW5CLGdCQUFnQjtvQ0FBUzs4Q0FDbEU7d0NBQUM7d0NBQUc7d0NBQUc7d0NBQUc7d0NBQUc7d0NBQUc7d0NBQUc7cUNBQUUsQ0FBQ29CLEdBQUcsQ0FBQyxDQUFDRSx1QkFDMUIsOERBQUMvQjs0Q0FBaUJFLE9BQU87Z0RBQUVLLFNBQVM7Z0RBQVFZLGVBQWU7NENBQVM7c0RBRWpFL0QsS0FBSzRFLEtBQUssQ0FBQyxHQUFHRCxRQUFRRixHQUFHLENBQUMsQ0FBQy9CLE1BQU1nQyxzQkFDaEMsOERBQUNqQztvREFFQ0MsTUFBTTt3REFBRSxHQUFHQSxJQUFJO3dEQUFFM0IsUUFBUTJELFVBQVVDLFNBQVM7b0RBQUU7b0RBQzlDaEMsU0FBUzt3REFDUCx5QkFBeUI7d0RBQ3pCLE1BQU1sQyxVQUFVOytEQUFJVDt5REFBSzt3REFDekJTLE9BQU8sQ0FBQ2lFLE1BQU0sQ0FBQzNELE1BQU0sR0FBRyxDQUFDTixPQUFPLENBQUNpRSxNQUFNLENBQUMzRCxNQUFNO3dEQUM5Q2QsUUFBUVE7b0RBQ1Y7bURBUEtpRTs7Ozs7MkNBSkRDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUF1QjVCO0dBbFB3QnRGO0tBQUFBIiwic291cmNlcyI6WyJFOlxcd2luOThcXHdpbjk4XFxzcmNcXGFwcFxcY29tcG9uZW50c1xcU29saXRhaXJlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIFNvbGl0YWlyZVByb3BzIHtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgaW5pdGlhbFBvc2l0aW9uOiB7IHg6IG51bWJlcjsgeTogbnVtYmVyIH07XG59XG5cbmludGVyZmFjZSBDYXJkIHtcbiAgc3VpdDogJ+KZoCcgfCAn4pmlJyB8ICfimaYnIHwgJ+KZoyc7XG4gIHZhbHVlOiBzdHJpbmc7XG4gIGNvbG9yOiAncmVkJyB8ICdibGFjayc7XG4gIGZhY2VVcDogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU29saXRhaXJlKHsgb25DbG9zZSwgaW5pdGlhbFBvc2l0aW9uIH06IFNvbGl0YWlyZVByb3BzKSB7XG4gIGNvbnN0IFtwb3NpdGlvbiwgc2V0UG9zaXRpb25dID0gdXNlU3RhdGUoaW5pdGlhbFBvc2l0aW9uKTtcbiAgY29uc3QgW2lzRHJhZ2dpbmcsIHNldElzRHJhZ2dpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZHJhZ09mZnNldCwgc2V0RHJhZ09mZnNldF0gPSB1c2VTdGF0ZSh7IHg6IDAsIHk6IDAgfSk7XG4gIGNvbnN0IFtkZWNrLCBzZXREZWNrXSA9IHVzZVN0YXRlPENhcmRbXT4oW10pO1xuICBjb25zdCBbZ2FtZVN0YXJ0ZWQsIHNldEdhbWVTdGFydGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgXG4gIGNvbnN0IHdpbmRvd1JlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IHRpdGxlQmFyUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICBjb25zdCBzdWl0czogQXJyYXk8J+KZoCcgfCAn4pmlJyB8ICfimaYnIHwgJ+KZoyc+ID0gWyfimaAnLCAn4pmlJywgJ+KZpicsICfimaMnXTtcbiAgY29uc3QgdmFsdWVzID0gWydBJywgJzInLCAnMycsICc0JywgJzUnLCAnNicsICc3JywgJzgnLCAnOScsICcxMCcsICdKJywgJ1EnLCAnSyddO1xuXG4gIGNvbnN0IGNyZWF0ZURlY2sgPSAoKTogQ2FyZFtdID0+IHtcbiAgICBjb25zdCBuZXdEZWNrOiBDYXJkW10gPSBbXTtcbiAgICBzdWl0cy5mb3JFYWNoKHN1aXQgPT4ge1xuICAgICAgdmFsdWVzLmZvckVhY2godmFsdWUgPT4ge1xuICAgICAgICBuZXdEZWNrLnB1c2goe1xuICAgICAgICAgIHN1aXQsXG4gICAgICAgICAgdmFsdWUsXG4gICAgICAgICAgY29sb3I6IHN1aXQgPT09ICfimaUnIHx8IHN1aXQgPT09ICfimaYnID8gJ3JlZCcgOiAnYmxhY2snLFxuICAgICAgICAgIGZhY2VVcDogZmFsc2VcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9KTtcbiAgICByZXR1cm4gc2h1ZmZsZURlY2sobmV3RGVjayk7XG4gIH07XG5cbiAgY29uc3Qgc2h1ZmZsZURlY2sgPSAoZGVjazogQ2FyZFtdKTogQ2FyZFtdID0+IHtcbiAgICBjb25zdCBzaHVmZmxlZCA9IFsuLi5kZWNrXTtcbiAgICBmb3IgKGxldCBpID0gc2h1ZmZsZWQubGVuZ3RoIC0gMTsgaSA+IDA7IGktLSkge1xuICAgICAgY29uc3QgaiA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIChpICsgMSkpO1xuICAgICAgW3NodWZmbGVkW2ldLCBzaHVmZmxlZFtqXV0gPSBbc2h1ZmZsZWRbal0sIHNodWZmbGVkW2ldXTtcbiAgICB9XG4gICAgcmV0dXJuIHNodWZmbGVkO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU1vdXNlRG93biA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG4gICAgaWYgKHRpdGxlQmFyUmVmLmN1cnJlbnQgJiYgdGl0bGVCYXJSZWYuY3VycmVudC5jb250YWlucyhlLnRhcmdldCBhcyBOb2RlKSkge1xuICAgICAgc2V0SXNEcmFnZ2luZyh0cnVlKTtcbiAgICAgIGNvbnN0IHJlY3QgPSB3aW5kb3dSZWYuY3VycmVudD8uZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XG4gICAgICBpZiAocmVjdCkge1xuICAgICAgICBzZXREcmFnT2Zmc2V0KHtcbiAgICAgICAgICB4OiBlLmNsaWVudFggLSByZWN0LmxlZnQsXG4gICAgICAgICAgeTogZS5jbGllbnRZIC0gcmVjdC50b3BcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU1vdXNlTW92ZSA9IChlOiBNb3VzZUV2ZW50KSA9PiB7XG4gICAgaWYgKGlzRHJhZ2dpbmcpIHtcbiAgICAgIHNldFBvc2l0aW9uKHtcbiAgICAgICAgeDogZS5jbGllbnRYIC0gZHJhZ09mZnNldC54LFxuICAgICAgICB5OiBlLmNsaWVudFkgLSBkcmFnT2Zmc2V0LnlcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVNb3VzZVVwID0gKCkgPT4ge1xuICAgIHNldElzRHJhZ2dpbmcoZmFsc2UpO1xuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzRHJhZ2dpbmcpIHtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIGhhbmRsZU1vdXNlTW92ZSk7XG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZXVwJywgaGFuZGxlTW91c2VVcCk7XG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZW1vdmUnLCBoYW5kbGVNb3VzZU1vdmUpO1xuICAgICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZXVwJywgaGFuZGxlTW91c2VVcCk7XG4gICAgICB9O1xuICAgIH1cbiAgfSwgW2lzRHJhZ2dpbmcsIGRyYWdPZmZzZXRdKTtcblxuICBjb25zdCBzdGFydE5ld0dhbWUgPSAoKSA9PiB7XG4gICAgc2V0RGVjayhjcmVhdGVEZWNrKCkpO1xuICAgIHNldEdhbWVTdGFydGVkKHRydWUpO1xuICB9O1xuXG4gIGNvbnN0IENhcmRDb21wb25lbnQgPSAoeyBjYXJkLCBvbkNsaWNrIH06IHsgY2FyZDogQ2FyZDsgb25DbGljaz86ICgpID0+IHZvaWQgfSkgPT4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT1cInNvbGl0YWlyZS1jYXJkXCJcbiAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XG4gICAgICBzdHlsZT17e1xuICAgICAgICB3aWR0aDogJzUwcHgnLFxuICAgICAgICBoZWlnaHQ6ICc3MHB4JyxcbiAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHZhcigtLXdpbjk4LWJsYWNrKScsXG4gICAgICAgIGJhY2tncm91bmQ6IGNhcmQuZmFjZVVwID8gJ3doaXRlJyA6ICd2YXIoLS13aW45OC1ibHVlKScsXG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgZm9udFNpemU6ICcxMnB4JyxcbiAgICAgICAgY29sb3I6IGNhcmQuZmFjZVVwID8gY2FyZC5jb2xvciA6ICd3aGl0ZScsXG4gICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICBtYXJnaW46ICcycHgnXG4gICAgICB9fVxuICAgID5cbiAgICAgIHtjYXJkLmZhY2VVcCA/IGAke2NhcmQudmFsdWV9JHtjYXJkLnN1aXR9YCA6ICfwn4KgJ31cbiAgICA8L2Rpdj5cbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIHJlZj17d2luZG93UmVmfVxuICAgICAgY2xhc3NOYW1lPVwid2luOTgtd2luZG93XCJcbiAgICAgIHN0eWxlPXt7XG4gICAgICAgIGxlZnQ6IGAke3Bvc2l0aW9uLnh9cHhgLFxuICAgICAgICB0b3A6IGAke3Bvc2l0aW9uLnl9cHhgLFxuICAgICAgICB3aWR0aDogJzUwMHB4JyxcbiAgICAgICAgaGVpZ2h0OiAnNDAwcHgnLFxuICAgICAgICB6SW5kZXg6IDEwMFxuICAgICAgfX1cbiAgICAgIG9uTW91c2VEb3duPXtoYW5kbGVNb3VzZURvd259XG4gICAgPlxuICAgICAgPGRpdiByZWY9e3RpdGxlQmFyUmVmfSBjbGFzc05hbWU9XCJ3aW45OC10aXRsZS1iYXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3aW45OC10aXRsZS10ZXh0XCI+8J+DjyBTb2xpdGFpcmU8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3aW45OC13aW5kb3ctY29udHJvbHNcIj5cbiAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cIndpbjk4LWNvbnRyb2wtYnV0dG9uXCI+XzwvYnV0dG9uPlxuICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwid2luOTgtY29udHJvbC1idXR0b25cIj7ilqE8L2J1dHRvbj5cbiAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cIndpbjk4LWNvbnRyb2wtYnV0dG9uXCIgb25DbGljaz17b25DbG9zZX0+w5c8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICAgIFxuICAgICAgPGRpdiBzdHlsZT17eyBcbiAgICAgICAgcGFkZGluZzogJzhweCcsIFxuICAgICAgICBiYWNrZ3JvdW5kOiAndmFyKC0td2luOTgtZ3JheSknLCBcbiAgICAgICAgaGVpZ2h0OiAnY2FsYygxMDAlIC0gMThweCknLFxuICAgICAgICBvdmVyZmxvdzogJ2F1dG8nXG4gICAgICB9fT5cbiAgICAgICAgeyFnYW1lU3RhcnRlZCA/IChcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IFxuICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLCBcbiAgICAgICAgICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLCBcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLCBcbiAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgIGhlaWdodDogJzEwMCUnXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8aDMgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMjBweCcgfX0+V2luZG93cyBTb2xpdGFpcmU8L2gzPlxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3aW45OC1idXR0b25cIiBvbkNsaWNrPXtzdGFydE5ld0dhbWV9PlxuICAgICAgICAgICAgICBOZXcgR2FtZVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IFxuICAgICAgICAgICAgICBtYXJnaW5Ub3A6ICcyMHB4JywgXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMTFweCcsIFxuICAgICAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgICAgICAgICAgICBjb2xvcjogJ3ZhcigtLXdpbjk4LXNoYWRvdy1kYXJrKSdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8cD5DbGFzc2ljIEtsb25kaWtlIFNvbGl0YWlyZTwvcD5cbiAgICAgICAgICAgICAgPHA+Q2xpY2sgY2FyZHMgdG8gZmxpcCB0aGVtPC9wPlxuICAgICAgICAgICAgICA8cD5EcmFnIGNhcmRzIHRvIG1vdmUgdGhlbTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICB7LyogR2FtZSBNZW51ICovfVxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxMHB4JyB9fT5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3aW45OC1idXR0b25cIiBvbkNsaWNrPXtzdGFydE5ld0dhbWV9PlxuICAgICAgICAgICAgICAgIE5ldyBHYW1lXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBtYXJnaW5MZWZ0OiAnMjBweCcsIGZvbnRTaXplOiAnMTFweCcgfX0+XG4gICAgICAgICAgICAgICAgQ2FyZHM6IHtkZWNrLmxlbmd0aH1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBHYW1lIEFyZWEgKi99XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAndmFyKC0td2luOTgtZGVza3RvcCknLCBcbiAgICAgICAgICAgICAgbWluSGVpZ2h0OiAnMzAwcHgnLFxuICAgICAgICAgICAgICBib3JkZXI6ICcycHggaW5zZXQgdmFyKC0td2luOTgtZ3JheSknLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMTBweCdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICB7LyogVG9wIFJvdyAtIEZvdW5kYXRpb24gYW5kIFN0b2NrICovfVxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgbWFyZ2luQm90dG9tOiAnMjBweCcgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzEwcHgnIH19PlxuICAgICAgICAgICAgICAgICAgey8qIFN0b2NrIHBpbGUgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnNXB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3aW45OC1ib3JkZXItaW5zZXRcIiBzdHlsZT17eyBcbiAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzU0cHgnLCBcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICc3NHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTBweCdcbiAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgU3RvY2tcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid2luOTgtYm9yZGVyLWluc2V0XCIgc3R5bGU9e3sgXG4gICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICc1NHB4JywgXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNzRweCcsXG4gICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEwcHgnXG4gICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgIFdhc3RlXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogRm91bmRhdGlvbiBwaWxlcyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnNXB4JyB9fT5cbiAgICAgICAgICAgICAgICAgIHtzdWl0cy5tYXAoKHN1aXQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwid2luOTgtYm9yZGVyLWluc2V0XCIgc3R5bGU9e3sgXG4gICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICc1NHB4JywgXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNzRweCcsXG4gICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE2cHgnXG4gICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgIHtzdWl0fVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogVGFibGVhdSAqL31cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzEwcHgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICAgICAge1sxLCAyLCAzLCA0LCA1LCA2LCA3XS5tYXAoKGNvbHVtbikgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2NvbHVtbn0gc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgey8qIFNob3cgc29tZSBzYW1wbGUgY2FyZHMgZm9yIGRlbW8gKi99XG4gICAgICAgICAgICAgICAgICAgIHtkZWNrLnNsaWNlKDAsIGNvbHVtbikubWFwKChjYXJkLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQ29tcG9uZW50XG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2FyZD17eyAuLi5jYXJkLCBmYWNlVXA6IGluZGV4ID09PSBjb2x1bW4gLSAxIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFNpbXBsZSBjYXJkIGZsaXAgbG9naWNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3RGVjayA9IFsuLi5kZWNrXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3RGVja1tpbmRleF0uZmFjZVVwID0gIW5ld0RlY2tbaW5kZXhdLmZhY2VVcDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0RGVjayhuZXdEZWNrKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiU29saXRhaXJlIiwib25DbG9zZSIsImluaXRpYWxQb3NpdGlvbiIsInBvc2l0aW9uIiwic2V0UG9zaXRpb24iLCJpc0RyYWdnaW5nIiwic2V0SXNEcmFnZ2luZyIsImRyYWdPZmZzZXQiLCJzZXREcmFnT2Zmc2V0IiwieCIsInkiLCJkZWNrIiwic2V0RGVjayIsImdhbWVTdGFydGVkIiwic2V0R2FtZVN0YXJ0ZWQiLCJ3aW5kb3dSZWYiLCJ0aXRsZUJhclJlZiIsInN1aXRzIiwidmFsdWVzIiwiY3JlYXRlRGVjayIsIm5ld0RlY2siLCJmb3JFYWNoIiwic3VpdCIsInZhbHVlIiwicHVzaCIsImNvbG9yIiwiZmFjZVVwIiwic2h1ZmZsZURlY2siLCJzaHVmZmxlZCIsImkiLCJsZW5ndGgiLCJqIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwiaGFuZGxlTW91c2VEb3duIiwiZSIsImN1cnJlbnQiLCJjb250YWlucyIsInRhcmdldCIsInJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJjbGllbnRYIiwibGVmdCIsImNsaWVudFkiLCJ0b3AiLCJoYW5kbGVNb3VzZU1vdmUiLCJoYW5kbGVNb3VzZVVwIiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInN0YXJ0TmV3R2FtZSIsIkNhcmRDb21wb25lbnQiLCJjYXJkIiwib25DbGljayIsImRpdiIsImNsYXNzTmFtZSIsInN0eWxlIiwid2lkdGgiLCJoZWlnaHQiLCJib3JkZXIiLCJiYWNrZ3JvdW5kIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImZvbnRTaXplIiwiY3Vyc29yIiwibWFyZ2luIiwicmVmIiwiekluZGV4Iiwib25Nb3VzZURvd24iLCJidXR0b24iLCJwYWRkaW5nIiwib3ZlcmZsb3ciLCJmbGV4RGlyZWN0aW9uIiwiaDMiLCJtYXJnaW5Cb3R0b20iLCJtYXJnaW5Ub3AiLCJ0ZXh0QWxpZ24iLCJwIiwic3BhbiIsIm1hcmdpbkxlZnQiLCJtaW5IZWlnaHQiLCJnYXAiLCJtYXAiLCJpbmRleCIsImNvbHVtbiIsInNsaWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Solitaire.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/Winamp.tsx":
/*!***************************************!*\
  !*** ./src/app/components/Winamp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Winamp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Winamp(param) {\n    let { onClose, initialPosition } = param;\n    var _playlist_currentSong, _playlist_currentSong1;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [currentSong, setCurrentSong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Sample songs - you can add MP3 files to public/music/ folder\n    const playlist = [\n        {\n            name: \"Sample Song 1\",\n            url: \"/music/sample1.mp3\"\n        },\n        {\n            name: \"Sample Song 2\",\n            url: \"/music/sample2.mp3\"\n        },\n        {\n            name: \"Sample Song 3\",\n            url: \"/music/sample3.mp3\"\n        }\n    ];\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Winamp.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Winamp.useEffect\"];\n            }\n        }\n    }[\"Winamp.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const updateTime = {\n                \"Winamp.useEffect.updateTime\": ()=>setCurrentTime(audio.currentTime)\n            }[\"Winamp.useEffect.updateTime\"];\n            const updateDuration = {\n                \"Winamp.useEffect.updateDuration\": ()=>setDuration(audio.duration)\n            }[\"Winamp.useEffect.updateDuration\"];\n            audio.addEventListener('timeupdate', updateTime);\n            audio.addEventListener('loadedmetadata', updateDuration);\n            return ({\n                \"Winamp.useEffect\": ()=>{\n                    audio.removeEventListener('timeupdate', updateTime);\n                    audio.removeEventListener('loadedmetadata', updateDuration);\n                }\n            })[\"Winamp.useEffect\"];\n        }\n    }[\"Winamp.useEffect\"], []);\n    const togglePlay = ()=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        if (isPlaying) {\n            audio.pause();\n        } else {\n            audio.play().catch(console.error);\n        }\n        setIsPlaying(!isPlaying);\n    };\n    const handleVolumeChange = (e)=>{\n        const newVolume = parseInt(e.target.value);\n        setVolume(newVolume);\n        if (audioRef.current) {\n            audioRef.current.volume = newVolume / 100;\n        }\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const nextSong = ()=>{\n        setCurrentSong((prev)=>(prev + 1) % playlist.length);\n        setIsPlaying(false);\n    };\n    const prevSong = ()=>{\n        setCurrentSong((prev)=>(prev - 1 + playlist.length) % playlist.length);\n        setIsPlaying(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        className: \"win98-window\",\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '300px',\n            height: '200px',\n            zIndex: 100\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                className: \"win98-title-bar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-title-text\",\n                        children: \"\\uD83C\\uDFB5 Winamp\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-window-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"_\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"□\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '8px',\n                    background: 'var(--win98-gray)',\n                    height: 'calc(100% - 18px)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: (_playlist_currentSong = playlist[currentSong]) === null || _playlist_currentSong === void 0 ? void 0 : _playlist_currentSong.url,\n                        onEnded: nextSong\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-border-inset\",\n                        style: {\n                            padding: '4px',\n                            background: '#000',\n                            color: '#00ff00',\n                            fontFamily: 'monospace',\n                            fontSize: '12px',\n                            marginBottom: '8px',\n                            height: '40px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: ((_playlist_currentSong1 = playlist[currentSong]) === null || _playlist_currentSong1 === void 0 ? void 0 : _playlist_currentSong1.name) || 'No song loaded'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    formatTime(currentTime),\n                                    \" / \",\n                                    formatTime(duration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '4px',\n                            marginBottom: '8px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: prevSong,\n                                children: \"⏮️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: togglePlay,\n                                children: isPlaying ? '⏸️' : '▶️'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: nextSong,\n                                children: \"⏭️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                children: \"⏹️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '10px'\n                                },\n                                children: \"Vol:\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"range\",\n                                min: \"0\",\n                                max: \"100\",\n                                value: volume,\n                                onChange: handleVolumeChange,\n                                style: {\n                                    flex: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '10px',\n                                    minWidth: '30px'\n                                },\n                                children: [\n                                    volume,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '9px',\n                            marginTop: '8px',\n                            color: 'var(--win98-shadow-dark)',\n                            textAlign: 'center'\n                        },\n                        children: \"Add MP3 files to /public/music/ folder\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(Winamp, \"fTXxXNoOGEUXl4TnJvZApy/sQ8Q=\");\n_c = Winamp;\nvar _c;\n$RefreshReg$(_c, \"Winamp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Winamp.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/DesktopIcon */ \"(app-pages-browser)/./src/app/components/DesktopIcon.tsx\");\n/* harmony import */ var _components_ChatWindow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ChatWindow */ \"(app-pages-browser)/./src/app/components/ChatWindow.tsx\");\n/* harmony import */ var _components_Taskbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Taskbar */ \"(app-pages-browser)/./src/app/components/Taskbar.tsx\");\n/* harmony import */ var _components_Winamp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/Winamp */ \"(app-pages-browser)/./src/app/components/Winamp.tsx\");\n/* harmony import */ var _components_Solitaire__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/Solitaire */ \"(app-pages-browser)/./src/app/components/Solitaire.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isWinampOpen, setIsWinampOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSolitaireOpen, setIsSolitaireOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleIconDoubleClick = ()=>{\n        setIsChatOpen(true);\n    };\n    const handleCloseChat = ()=>{\n        setIsChatOpen(false);\n    };\n    const handleCloseWinamp = ()=>{\n        setIsWinampOpen(false);\n    };\n    const handleCloseSolitaire = ()=>{\n        setIsSolitaireOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"win98-desktop\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCAC\",\n                label: \"Chat with Sheila\",\n                position: {\n                    x: 50,\n                    y: 50\n                },\n                onDoubleClick: handleIconDoubleClick\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDDA5️\",\n                label: \"My Computer\",\n                position: {\n                    x: 50,\n                    y: 140\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCC2\",\n                label: \"My Documents\",\n                position: {\n                    x: 50,\n                    y: 230\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDDD1️\",\n                label: \"Recycle Bin\",\n                position: {\n                    x: 50,\n                    y: 320\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDF0D\",\n                label: \"Internet Explorer\",\n                position: {\n                    x: 50,\n                    y: 410\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"✉️\",\n                label: \"Outlook Express\",\n                position: {\n                    x: 150,\n                    y: 50\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDFB5\",\n                label: \"Winamp\",\n                position: {\n                    x: 150,\n                    y: 140\n                },\n                onDoubleClick: ()=>setIsWinampOpen(true)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDCCF\",\n                label: \"Solitaire\",\n                position: {\n                    x: 150,\n                    y: 230\n                },\n                onDoubleClick: ()=>setIsSolitaireOpen(true)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDD8C️\",\n                label: \"Paint\",\n                position: {\n                    x: 150,\n                    y: 320\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCC4\",\n                label: \"Notepad\",\n                position: {\n                    x: 150,\n                    y: 410\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"⚙️\",\n                label: \"Control Panel\",\n                position: {\n                    x: 250,\n                    y: 50\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCBE\",\n                label: \"3\\xbd Floppy (A:)\",\n                position: {\n                    x: 250,\n                    y: 140\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCBF\",\n                label: \"CD-ROM (D:)\",\n                position: {\n                    x: 250,\n                    y: 230\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            isChatOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                title: \"Chat with Sheila\",\n                onClose: handleCloseChat,\n                initialPosition: {\n                    x: 200,\n                    y: 100\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this),\n            isWinampOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Winamp__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onClose: handleCloseWinamp,\n                initialPosition: {\n                    x: 300,\n                    y: 150\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this),\n            isSolitaireOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Solitaire__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClose: handleCloseSolitaire,\n                initialPosition: {\n                    x: 250,\n                    y: 80\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Taskbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"OYANax6QzOWMqYobFxeNIHbivy4=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});