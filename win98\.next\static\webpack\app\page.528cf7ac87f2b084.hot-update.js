"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/DesktopIcon */ \"(app-pages-browser)/./src/app/components/DesktopIcon.tsx\");\n/* harmony import */ var _components_ChatWindow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ChatWindow */ \"(app-pages-browser)/./src/app/components/ChatWindow.tsx\");\n/* harmony import */ var _components_Taskbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Taskbar */ \"(app-pages-browser)/./src/app/components/Taskbar.tsx\");\n/* harmony import */ var _components_Winamp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/Winamp */ \"(app-pages-browser)/./src/app/components/Winamp.tsx\");\n/* harmony import */ var _components_Solitaire__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/Solitaire */ \"(app-pages-browser)/./src/app/components/Solitaire.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isWinampOpen, setIsWinampOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSolitaireOpen, setIsSolitaireOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleIconDoubleClick = ()=>{\n        setIsChatOpen(true);\n    };\n    const handleCloseChat = ()=>{\n        setIsChatOpen(false);\n    };\n    const handleCloseWinamp = ()=>{\n        setIsWinampOpen(false);\n    };\n    const handleCloseSolitaire = ()=>{\n        setIsSolitaireOpen(false);\n    };\n    const handleDesktopClick = ()=>{\n    // This will be passed to DesktopIcon to deselect all icons\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"win98-desktop\",\n        onClick: handleDesktopClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCAC\",\n                label: \"Chat with Sheila\",\n                position: {\n                    x: 50,\n                    y: 50\n                },\n                onDoubleClick: handleIconDoubleClick\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDDA5️\",\n                label: \"My Computer\",\n                position: {\n                    x: 50,\n                    y: 140\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCC2\",\n                label: \"My Documents\",\n                position: {\n                    x: 50,\n                    y: 230\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDDD1️\",\n                label: \"Recycle Bin\",\n                position: {\n                    x: 50,\n                    y: 320\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDF0D\",\n                label: \"Internet Explorer\",\n                position: {\n                    x: 50,\n                    y: 410\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"✉️\",\n                label: \"Outlook Express\",\n                position: {\n                    x: 150,\n                    y: 50\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDFB5\",\n                label: \"Winamp\",\n                position: {\n                    x: 150,\n                    y: 140\n                },\n                onDoubleClick: ()=>setIsWinampOpen(true)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDCCF\",\n                label: \"Solitaire\",\n                position: {\n                    x: 150,\n                    y: 230\n                },\n                onDoubleClick: ()=>setIsSolitaireOpen(true)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDD8C️\",\n                label: \"Paint\",\n                position: {\n                    x: 150,\n                    y: 320\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCC4\",\n                label: \"Notepad\",\n                position: {\n                    x: 150,\n                    y: 410\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"⚙️\",\n                label: \"Control Panel\",\n                position: {\n                    x: 250,\n                    y: 50\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCBE\",\n                label: \"3\\xbd Floppy (A:)\",\n                position: {\n                    x: 250,\n                    y: 140\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCBF\",\n                label: \"CD-ROM (D:)\",\n                position: {\n                    x: 250,\n                    y: 230\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            isChatOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                title: \"Chat with Sheila\",\n                onClose: handleCloseChat,\n                initialPosition: {\n                    x: 200,\n                    y: 100\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this),\n            isWinampOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Winamp__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onClose: handleCloseWinamp,\n                initialPosition: {\n                    x: 300,\n                    y: 150\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this),\n            isSolitaireOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Solitaire__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClose: handleCloseSolitaire,\n                initialPosition: {\n                    x: 250,\n                    y: 80\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Taskbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"OYANax6QzOWMqYobFxeNIHbivy4=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});