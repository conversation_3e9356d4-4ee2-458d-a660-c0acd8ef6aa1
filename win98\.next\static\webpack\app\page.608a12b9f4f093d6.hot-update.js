"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/Solitaire.tsx":
/*!******************************************!*\
  !*** ./src/app/components/Solitaire.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Solitaire)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Solitaire(param) {\n    let { onClose, initialPosition } = param;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [deck, setDeck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tableau, setTableau] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        []\n    ]);\n    const [stock, setStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [waste, setWaste] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [foundations, setFoundations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        [],\n        [],\n        [],\n        []\n    ]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const suits = [\n        '♠',\n        '♥',\n        '♦',\n        '♣'\n    ];\n    const values = [\n        'A',\n        '2',\n        '3',\n        '4',\n        '5',\n        '6',\n        '7',\n        '8',\n        '9',\n        '10',\n        'J',\n        'Q',\n        'K'\n    ];\n    const createDeck = ()=>{\n        const newDeck = [];\n        suits.forEach((suit)=>{\n            values.forEach((value)=>{\n                newDeck.push({\n                    suit,\n                    value,\n                    color: suit === '♥' || suit === '♦' ? 'red' : 'black',\n                    faceUp: false\n                });\n            });\n        });\n        return shuffleDeck(newDeck);\n    };\n    const shuffleDeck = (deck)=>{\n        const shuffled = [\n            ...deck\n        ];\n        for(let i = shuffled.length - 1; i > 0; i--){\n            const j = Math.floor(Math.random() * (i + 1));\n            [shuffled[i], shuffled[j]] = [\n                shuffled[j],\n                shuffled[i]\n            ];\n        }\n        return shuffled;\n    };\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Solitaire.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Solitaire.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Solitaire.useEffect\"];\n            }\n        }\n    }[\"Solitaire.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    const startNewGame = ()=>{\n        setDeck(createDeck());\n        setGameStarted(true);\n    };\n    const CardComponent = (param)=>{\n        let { card, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"solitaire-card\",\n            onClick: onClick,\n            style: {\n                width: '50px',\n                height: '70px',\n                border: '1px solid var(--win98-black)',\n                background: card.faceUp ? 'white' : 'var(--win98-blue)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                color: card.faceUp ? card.color : 'white',\n                cursor: 'pointer',\n                margin: '2px'\n            },\n            children: card.faceUp ? \"\".concat(card.value).concat(card.suit) : '🂠'\n        }, void 0, false, {\n            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n            lineNumber: 103,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        className: \"win98-window\",\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '500px',\n            height: '400px',\n            zIndex: 100\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                className: \"win98-title-bar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-title-text\",\n                        children: \"\\uD83C\\uDCCF Solitaire\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-window-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"_\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"□\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '8px',\n                    background: 'var(--win98-gray)',\n                    height: 'calc(100% - 18px)',\n                    overflow: 'auto'\n                },\n                children: !gameStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        height: '100%'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                marginBottom: '20px'\n                            },\n                            children: \"Windows Solitaire\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"win98-button\",\n                            onClick: startNewGame,\n                            children: \"New Game\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '20px',\n                                fontSize: '11px',\n                                textAlign: 'center',\n                                color: 'var(--win98-shadow-dark)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Classic Klondike Solitaire\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Click cards to flip them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Drag cards to move them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"win98-button\",\n                                    onClick: startNewGame,\n                                    children: \"New Game\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginLeft: '20px',\n                                        fontSize: '11px'\n                                    },\n                                    children: [\n                                        \"Cards: \",\n                                        deck.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'var(--win98-desktop)',\n                                minHeight: '300px',\n                                border: '2px inset var(--win98-gray)',\n                                padding: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '10px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '5px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px'\n                                                        },\n                                                        children: \"Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px'\n                                                        },\n                                                        children: \"Waste\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '5px'\n                                            },\n                                            children: suits.map((suit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"win98-border-inset\",\n                                                    style: {\n                                                        width: '54px',\n                                                        height: '74px',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: suit\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '10px',\n                                        justifyContent: 'center'\n                                    },\n                                    children: [\n                                        1,\n                                        2,\n                                        3,\n                                        4,\n                                        5,\n                                        6,\n                                        7\n                                    ].map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column'\n                                            },\n                                            children: deck.slice(0, column).map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardComponent, {\n                                                    card: {\n                                                        ...card,\n                                                        faceUp: index === column - 1\n                                                    },\n                                                    onClick: ()=>{\n                                                        // Simple card flip logic\n                                                        const newDeck = [\n                                                            ...deck\n                                                        ];\n                                                        newDeck[index].faceUp = !newDeck[index].faceUp;\n                                                        setDeck(newDeck);\n                                                    }\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, column, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(Solitaire, \"/kxWNHzc9vXkpHhiXjDak8TF2cY=\");\n_c = Solitaire;\nvar _c;\n$RefreshReg$(_c, \"Solitaire\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Solitaire.tsx\n"));

/***/ })

});