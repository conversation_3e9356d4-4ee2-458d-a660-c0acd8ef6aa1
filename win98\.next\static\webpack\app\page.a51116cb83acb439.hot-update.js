"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/Solitaire.tsx":
/*!******************************************!*\
  !*** ./src/app/components/Solitaire.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Solitaire)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Solitaire(param) {\n    let { onClose, initialPosition } = param;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [deck, setDeck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tableau, setTableau] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        []\n    ]);\n    const [stock, setStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [waste, setWaste] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [foundations, setFoundations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        [],\n        [],\n        [],\n        []\n    ]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const suits = [\n        '♠',\n        '♥',\n        '♦',\n        '♣'\n    ];\n    const values = [\n        'A',\n        '2',\n        '3',\n        '4',\n        '5',\n        '6',\n        '7',\n        '8',\n        '9',\n        '10',\n        'J',\n        'Q',\n        'K'\n    ];\n    const createDeck = ()=>{\n        const newDeck = [];\n        suits.forEach((suit)=>{\n            values.forEach((value)=>{\n                newDeck.push({\n                    suit,\n                    value,\n                    color: suit === '♥' || suit === '♦' ? 'red' : 'black',\n                    faceUp: false,\n                    id: \"\".concat(suit, \"-\").concat(value)\n                });\n            });\n        });\n        return shuffleDeck(newDeck);\n    };\n    const shuffleDeck = (deck)=>{\n        const shuffled = [\n            ...deck\n        ];\n        for(let i = shuffled.length - 1; i > 0; i--){\n            const j = Math.floor(Math.random() * (i + 1));\n            [shuffled[i], shuffled[j]] = [\n                shuffled[j],\n                shuffled[i]\n            ];\n        }\n        return shuffled;\n    };\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Solitaire.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Solitaire.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Solitaire.useEffect\"];\n            }\n        }\n    }[\"Solitaire.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    const startNewGame = ()=>{\n        const newDeck = createDeck();\n        // Deal cards to tableau\n        const newTableau = [\n            [],\n            [],\n            [],\n            [],\n            [],\n            [],\n            []\n        ];\n        let deckIndex = 0;\n        for(let col = 0; col < 7; col++){\n            for(let row = 0; row <= col; row++){\n                const card = newDeck[deckIndex++];\n                if (row === col) {\n                    card.faceUp = true; // Top card is face up\n                }\n                newTableau[col].push(card);\n            }\n        }\n        // Remaining cards go to stock\n        const remainingCards = newDeck.slice(deckIndex);\n        setTableau(newTableau);\n        setStock(remainingCards);\n        setWaste([]);\n        setFoundations([\n            [],\n            [],\n            [],\n            []\n        ]);\n        setScore(0);\n        setGameStarted(true);\n    };\n    const CardComponent = (param)=>{\n        let { card, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"solitaire-card\",\n            onClick: onClick,\n            style: {\n                width: '50px',\n                height: '70px',\n                border: '1px solid var(--win98-black)',\n                background: card.faceUp ? 'white' : 'var(--win98-blue)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                color: card.faceUp ? card.color : 'white',\n                cursor: 'pointer',\n                margin: '2px'\n            },\n            children: card.faceUp ? \"\".concat(card.value).concat(card.suit) : '🂠'\n        }, void 0, false, {\n            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n            lineNumber: 127,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        className: \"win98-window\",\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '500px',\n            height: '400px',\n            zIndex: 100\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                className: \"win98-title-bar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-title-text\",\n                        children: \"\\uD83C\\uDCCF Solitaire\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-window-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"_\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"□\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '8px',\n                    background: 'var(--win98-gray)',\n                    height: 'calc(100% - 18px)',\n                    overflow: 'auto'\n                },\n                children: !gameStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        height: '100%'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                marginBottom: '20px'\n                            },\n                            children: \"Windows Solitaire\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"win98-button\",\n                            onClick: startNewGame,\n                            children: \"New Game\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '20px',\n                                fontSize: '11px',\n                                textAlign: 'center',\n                                color: 'var(--win98-shadow-dark)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Classic Klondike Solitaire\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Click cards to flip them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Drag cards to move them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"win98-button\",\n                                    onClick: startNewGame,\n                                    children: \"New Game\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginLeft: '20px',\n                                        fontSize: '11px'\n                                    },\n                                    children: [\n                                        \"Cards: \",\n                                        deck.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'var(--win98-desktop)',\n                                minHeight: '300px',\n                                border: '2px inset var(--win98-gray)',\n                                padding: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '10px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '5px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px',\n                                                            cursor: stock.length > 0 ? 'pointer' : 'default',\n                                                            background: stock.length > 0 ? 'var(--win98-blue)' : 'var(--win98-gray)'\n                                                        },\n                                                        onClick: ()=>{\n                                                            if (stock.length > 0) {\n                                                                // Draw 3 cards from stock to waste\n                                                                const newStock = [\n                                                                    ...stock\n                                                                ];\n                                                                const newWaste = [\n                                                                    ...waste\n                                                                ];\n                                                                for(let i = 0; i < 3 && newStock.length > 0; i++){\n                                                                    const card = newStock.pop();\n                                                                    card.faceUp = true;\n                                                                    newWaste.push(card);\n                                                                }\n                                                                setStock(newStock);\n                                                                setWaste(newWaste);\n                                                            } else if (waste.length > 0) {\n                                                                // Reset: move all waste back to stock\n                                                                const newStock = [\n                                                                    ...waste\n                                                                ].reverse();\n                                                                newStock.forEach((card)=>card.faceUp = false);\n                                                                setStock(newStock);\n                                                                setWaste([]);\n                                                            }\n                                                        },\n                                                        children: stock.length > 0 ? '🂠' : '↻'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px',\n                                                            position: 'relative'\n                                                        },\n                                                        children: waste.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardComponent, {\n                                                            card: waste[waste.length - 1],\n                                                            onClick: ()=>{\n                                                                // TODO: Allow moving waste card to foundation or tableau\n                                                                console.log('Clicked waste card');\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 25\n                                                        }, this) : 'Waste'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '5px'\n                                            },\n                                            children: suits.map((suit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"win98-border-inset\",\n                                                    style: {\n                                                        width: '54px',\n                                                        height: '74px',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: suit\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '10px',\n                                        justifyContent: 'center'\n                                    },\n                                    children: tableau.map((column, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                minHeight: '100px',\n                                                minWidth: '54px'\n                                            },\n                                            children: column.length === 0 ? // Empty column placeholder\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"win98-border-inset\",\n                                                style: {\n                                                    width: '54px',\n                                                    height: '74px',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    fontSize: '10px',\n                                                    color: 'var(--win98-shadow-dark)'\n                                                },\n                                                children: colIndex + 1\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 23\n                                            }, this) : column.map((card, cardIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: cardIndex > 0 ? '-50px' : '0'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardComponent, {\n                                                        card: card,\n                                                        onClick: ()=>{\n                                                            if (!card.faceUp && cardIndex === column.length - 1) {\n                                                                // Flip the top face-down card\n                                                                const newTableau = [\n                                                                    ...tableau\n                                                                ];\n                                                                newTableau[colIndex][cardIndex].faceUp = true;\n                                                                setTableau(newTableau);\n                                                                setScore(score + 5);\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, card.id, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, colIndex, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(Solitaire, \"/kxWNHzc9vXkpHhiXjDak8TF2cY=\");\n_c = Solitaire;\nvar _c;\n$RefreshReg$(_c, \"Solitaire\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Solitaire.tsx\n"));

/***/ })

});