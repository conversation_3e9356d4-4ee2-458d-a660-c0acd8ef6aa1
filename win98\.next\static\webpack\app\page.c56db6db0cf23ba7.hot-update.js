"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/Solitaire.tsx":
/*!******************************************!*\
  !*** ./src/app/components/Solitaire.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Solitaire)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Solitaire(param) {\n    let { onClose, initialPosition } = param;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [deck, setDeck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gameStarted, setGameStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tableau, setTableau] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        [],\n        [],\n        [],\n        [],\n        [],\n        [],\n        []\n    ]);\n    const [stock, setStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [waste, setWaste] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [foundations, setFoundations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        [],\n        [],\n        [],\n        []\n    ]);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const suits = [\n        '♠',\n        '♥',\n        '♦',\n        '♣'\n    ];\n    const values = [\n        'A',\n        '2',\n        '3',\n        '4',\n        '5',\n        '6',\n        '7',\n        '8',\n        '9',\n        '10',\n        'J',\n        'Q',\n        'K'\n    ];\n    const createDeck = ()=>{\n        const newDeck = [];\n        suits.forEach((suit)=>{\n            values.forEach((value)=>{\n                newDeck.push({\n                    suit,\n                    value,\n                    color: suit === '♥' || suit === '♦' ? 'red' : 'black',\n                    faceUp: false,\n                    id: \"\".concat(suit, \"-\").concat(value)\n                });\n            });\n        });\n        return shuffleDeck(newDeck);\n    };\n    const shuffleDeck = (deck)=>{\n        const shuffled = [\n            ...deck\n        ];\n        for(let i = shuffled.length - 1; i > 0; i--){\n            const j = Math.floor(Math.random() * (i + 1));\n            [shuffled[i], shuffled[j]] = [\n                shuffled[j],\n                shuffled[i]\n            ];\n        }\n        return shuffled;\n    };\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Solitaire.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Solitaire.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Solitaire.useEffect\"];\n            }\n        }\n    }[\"Solitaire.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    const startNewGame = ()=>{\n        setDeck(createDeck());\n        setGameStarted(true);\n    };\n    const CardComponent = (param)=>{\n        let { card, onClick } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"solitaire-card\",\n            onClick: onClick,\n            style: {\n                width: '50px',\n                height: '70px',\n                border: '1px solid var(--win98-black)',\n                background: card.faceUp ? 'white' : 'var(--win98-blue)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '12px',\n                color: card.faceUp ? card.color : 'white',\n                cursor: 'pointer',\n                margin: '2px'\n            },\n            children: card.faceUp ? \"\".concat(card.value).concat(card.suit) : '🂠'\n        }, void 0, false, {\n            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n            lineNumber: 104,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        className: \"win98-window\",\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '500px',\n            height: '400px',\n            zIndex: 100\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                className: \"win98-title-bar\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-title-text\",\n                        children: \"\\uD83C\\uDCCF Solitaire\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-window-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"_\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                children: \"□\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-control-button\",\n                                onClick: onClose,\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '8px',\n                    background: 'var(--win98-gray)',\n                    height: 'calc(100% - 18px)',\n                    overflow: 'auto'\n                },\n                children: !gameStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        height: '100%'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                marginBottom: '20px'\n                            },\n                            children: \"Windows Solitaire\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"win98-button\",\n                            onClick: startNewGame,\n                            children: \"New Game\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '20px',\n                                fontSize: '11px',\n                                textAlign: 'center',\n                                color: 'var(--win98-shadow-dark)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Classic Klondike Solitaire\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Click cards to flip them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Drag cards to move them\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"win98-button\",\n                                    onClick: startNewGame,\n                                    children: \"New Game\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        marginLeft: '20px',\n                                        fontSize: '11px'\n                                    },\n                                    children: [\n                                        \"Cards: \",\n                                        deck.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'var(--win98-desktop)',\n                                minHeight: '300px',\n                                border: '2px inset var(--win98-gray)',\n                                padding: '10px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '10px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '5px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px'\n                                                        },\n                                                        children: \"Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"win98-border-inset\",\n                                                        style: {\n                                                            width: '54px',\n                                                            height: '74px',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '10px'\n                                                        },\n                                                        children: \"Waste\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '5px'\n                                            },\n                                            children: suits.map((suit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"win98-border-inset\",\n                                                    style: {\n                                                        width: '54px',\n                                                        height: '74px',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: suit\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '10px',\n                                        justifyContent: 'center'\n                                    },\n                                    children: [\n                                        1,\n                                        2,\n                                        3,\n                                        4,\n                                        5,\n                                        6,\n                                        7\n                                    ].map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column'\n                                            },\n                                            children: deck.slice(0, column).map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardComponent, {\n                                                    card: {\n                                                        ...card,\n                                                        faceUp: index === column - 1\n                                                    },\n                                                    onClick: ()=>{\n                                                        // Simple card flip logic\n                                                        const newDeck = [\n                                                            ...deck\n                                                        ];\n                                                        newDeck[index].faceUp = !newDeck[index].faceUp;\n                                                        setDeck(newDeck);\n                                                    }\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, column, false, {\n                                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Solitaire.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(Solitaire, \"/kxWNHzc9vXkpHhiXjDak8TF2cY=\");\n_c = Solitaire;\nvar _c;\n$RefreshReg$(_c, \"Solitaire\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Solitaire.tsx\n"));

/***/ })

});