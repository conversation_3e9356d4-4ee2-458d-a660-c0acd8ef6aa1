"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/Winamp.tsx":
/*!***************************************!*\
  !*** ./src/app/components/Winamp.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Winamp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Winamp(param) {\n    let { onClose, initialPosition } = param;\n    var _playlist_currentSong, _playlist_currentSong1;\n    _s();\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPosition);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [currentSong, setCurrentSong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const windowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleBarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [playlist, setPlaylist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load music files from public/music folder\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            const loadPlaylist = {\n                \"Winamp.useEffect.loadPlaylist\": async ()=>{\n                    try {\n                        // Try to fetch a list of files from the music directory\n                        // Since we can't directly read directory in browser, we'll use a predefined list\n                        // Users need to update this list when they add new files\n                        const musicFiles = [\n                            'sample1.mp3',\n                            'sample2.mp3',\n                            'sample3.mp3',\n                            'song1.mp3',\n                            'song2.mp3',\n                            'music.mp3',\n                            'track1.mp3',\n                            'track2.mp3'\n                        ];\n                        const availableSongs = [];\n                        for (const file of musicFiles){\n                            try {\n                                const response = await fetch(\"/music/\".concat(file), {\n                                    method: 'HEAD'\n                                });\n                                if (response.ok) {\n                                    availableSongs.push({\n                                        name: file.replace('.mp3', '').replace(/[-_]/g, ' '),\n                                        url: \"/music/\".concat(file)\n                                    });\n                                }\n                            } catch (error) {\n                            // File doesn't exist, skip it\n                            }\n                        }\n                        if (availableSongs.length === 0) {\n                            // Add default message if no songs found\n                            availableSongs.push({\n                                name: \"No songs found - Add MP3 files to /public/music/\",\n                                url: \"\"\n                            });\n                        }\n                        setPlaylist(availableSongs);\n                    } catch (error) {\n                        console.error('Error loading playlist:', error);\n                    }\n                }\n            }[\"Winamp.useEffect.loadPlaylist\"];\n            loadPlaylist();\n        }\n    }[\"Winamp.useEffect\"], []);\n    const handleMouseDown = (e)=>{\n        if (titleBarRef.current && titleBarRef.current.contains(e.target)) {\n            var _windowRef_current;\n            setIsDragging(true);\n            const rect = (_windowRef_current = windowRef.current) === null || _windowRef_current === void 0 ? void 0 : _windowRef_current.getBoundingClientRect();\n            if (rect) {\n                setDragOffset({\n                    x: e.clientX - rect.left,\n                    y: e.clientY - rect.top\n                });\n            }\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (isDragging) {\n            setPosition({\n                x: e.clientX - dragOffset.x,\n                y: e.clientY - dragOffset.y\n            });\n        }\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            if (isDragging) {\n                document.addEventListener('mousemove', handleMouseMove);\n                document.addEventListener('mouseup', handleMouseUp);\n                return ({\n                    \"Winamp.useEffect\": ()=>{\n                        document.removeEventListener('mousemove', handleMouseMove);\n                        document.removeEventListener('mouseup', handleMouseUp);\n                    }\n                })[\"Winamp.useEffect\"];\n            }\n        }\n    }[\"Winamp.useEffect\"], [\n        isDragging,\n        dragOffset\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Winamp.useEffect\": ()=>{\n            const audio = audioRef.current;\n            if (!audio) return;\n            const updateTime = {\n                \"Winamp.useEffect.updateTime\": ()=>setCurrentTime(audio.currentTime)\n            }[\"Winamp.useEffect.updateTime\"];\n            const updateDuration = {\n                \"Winamp.useEffect.updateDuration\": ()=>setDuration(audio.duration)\n            }[\"Winamp.useEffect.updateDuration\"];\n            audio.addEventListener('timeupdate', updateTime);\n            audio.addEventListener('loadedmetadata', updateDuration);\n            return ({\n                \"Winamp.useEffect\": ()=>{\n                    audio.removeEventListener('timeupdate', updateTime);\n                    audio.removeEventListener('loadedmetadata', updateDuration);\n                }\n            })[\"Winamp.useEffect\"];\n        }\n    }[\"Winamp.useEffect\"], []);\n    const togglePlay = ()=>{\n        const audio = audioRef.current;\n        if (!audio) return;\n        if (isPlaying) {\n            audio.pause();\n        } else {\n            audio.play().catch(console.error);\n        }\n        setIsPlaying(!isPlaying);\n    };\n    const handleVolumeChange = (e)=>{\n        const newVolume = parseInt(e.target.value);\n        setVolume(newVolume);\n        if (audioRef.current) {\n            audioRef.current.volume = newVolume / 100;\n        }\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const nextSong = ()=>{\n        setCurrentSong((prev)=>(prev + 1) % playlist.length);\n        setIsPlaying(false);\n    };\n    const prevSong = ()=>{\n        setCurrentSong((prev)=>(prev - 1 + playlist.length) % playlist.length);\n        setIsPlaying(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: windowRef,\n        style: {\n            left: \"\".concat(position.x, \"px\"),\n            top: \"\".concat(position.y, \"px\"),\n            width: '275px',\n            height: '116px',\n            zIndex: 100,\n            background: 'linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%)',\n            border: '2px outset #666',\n            borderRadius: '0',\n            position: 'absolute'\n        },\n        onMouseDown: handleMouseDown,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: titleBarRef,\n                style: {\n                    background: 'linear-gradient(90deg, #ff6600 0%, #ff3300 100%)',\n                    color: 'white',\n                    padding: '2px 4px',\n                    fontSize: '11px',\n                    fontWeight: 'bold',\n                    cursor: 'move',\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    height: '14px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"\\uD83C\\uDFB5 Winamp\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        style: {\n                            background: 'none',\n                            border: 'none',\n                            color: 'white',\n                            cursor: 'pointer',\n                            fontSize: '12px',\n                            padding: '0 4px'\n                        },\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: '4px',\n                    background: 'linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%)',\n                    height: 'calc(100% - 14px)',\n                    color: '#00ff00'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioRef,\n                        src: (_playlist_currentSong = playlist[currentSong]) === null || _playlist_currentSong === void 0 ? void 0 : _playlist_currentSong.url,\n                        onEnded: nextSong\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"win98-border-inset\",\n                        style: {\n                            padding: '4px',\n                            background: '#000',\n                            color: '#00ff00',\n                            fontFamily: 'monospace',\n                            fontSize: '12px',\n                            marginBottom: '8px',\n                            height: '40px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: ((_playlist_currentSong1 = playlist[currentSong]) === null || _playlist_currentSong1 === void 0 ? void 0 : _playlist_currentSong1.name) || 'No song loaded'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    formatTime(currentTime),\n                                    \" / \",\n                                    formatTime(duration)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '4px',\n                            marginBottom: '8px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: prevSong,\n                                children: \"⏮️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: togglePlay,\n                                children: isPlaying ? '⏸️' : '▶️'\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                onClick: nextSong,\n                                children: \"⏭️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"win98-button\",\n                                children: \"⏹️\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '10px'\n                                },\n                                children: \"Vol:\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"range\",\n                                min: \"0\",\n                                max: \"100\",\n                                value: volume,\n                                onChange: handleVolumeChange,\n                                style: {\n                                    flex: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '10px',\n                                    minWidth: '30px'\n                                },\n                                children: [\n                                    volume,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '9px',\n                            marginTop: '8px',\n                            color: 'var(--win98-shadow-dark)',\n                            textAlign: 'center'\n                        },\n                        children: \"Add MP3 files to /public/music/ folder\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\components\\\\Winamp.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n_s(Winamp, \"cf+PyMWo0aSzwZLtrM9qpXjbLZc=\");\n_c = Winamp;\nvar _c;\n$RefreshReg$(_c, \"Winamp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Winamp.tsx\n"));

/***/ })

});