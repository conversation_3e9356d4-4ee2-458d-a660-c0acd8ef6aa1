"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/DesktopIcon */ \"(app-pages-browser)/./src/app/components/DesktopIcon.tsx\");\n/* harmony import */ var _components_ChatWindow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ChatWindow */ \"(app-pages-browser)/./src/app/components/ChatWindow.tsx\");\n/* harmony import */ var _components_Taskbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Taskbar */ \"(app-pages-browser)/./src/app/components/Taskbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [isChatOpen, setIsChatOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isWinampOpen, setIsWinampOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSolitaireOpen, setIsSolitaireOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleIconDoubleClick = ()=>{\n        setIsChatOpen(true);\n    };\n    const handleCloseChat = ()=>{\n        setIsChatOpen(false);\n    };\n    const handleCloseWinamp = ()=>{\n        setIsWinampOpen(false);\n    };\n    const handleCloseSolitaire = ()=>{\n        setIsSolitaireOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"win98-desktop\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCAC\",\n                label: \"Chat with Sheila\",\n                position: {\n                    x: 50,\n                    y: 50\n                },\n                onDoubleClick: handleIconDoubleClick\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDDA5️\",\n                label: \"My Computer\",\n                position: {\n                    x: 50,\n                    y: 140\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCC2\",\n                label: \"My Documents\",\n                position: {\n                    x: 50,\n                    y: 230\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDDD1️\",\n                label: \"Recycle Bin\",\n                position: {\n                    x: 50,\n                    y: 320\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDF0D\",\n                label: \"Internet Explorer\",\n                position: {\n                    x: 50,\n                    y: 410\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"✉️\",\n                label: \"Outlook Express\",\n                position: {\n                    x: 150,\n                    y: 50\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDFB5\",\n                label: \"Winamp\",\n                position: {\n                    x: 150,\n                    y: 140\n                },\n                onDoubleClick: ()=>setIsWinampOpen(true)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83C\\uDCCF\",\n                label: \"Solitaire\",\n                position: {\n                    x: 150,\n                    y: 230\n                },\n                onDoubleClick: ()=>setIsSolitaireOpen(true)\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDD8C️\",\n                label: \"Paint\",\n                position: {\n                    x: 150,\n                    y: 320\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCC4\",\n                label: \"Notepad\",\n                position: {\n                    x: 150,\n                    y: 410\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"⚙️\",\n                label: \"Control Panel\",\n                position: {\n                    x: 250,\n                    y: 50\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCBE\",\n                label: \"3\\xbd Floppy (A:)\",\n                position: {\n                    x: 250,\n                    y: 140\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DesktopIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                icon: \"\\uD83D\\uDCBF\",\n                label: \"CD-ROM (D:)\",\n                position: {\n                    x: 250,\n                    y: 230\n                },\n                onDoubleClick: ()=>{}\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            isChatOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                title: \"Chat with Sheila\",\n                onClose: handleCloseChat,\n                initialPosition: {\n                    x: 200,\n                    y: 100\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Taskbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\win98\\\\win98\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"OYANax6QzOWMqYobFxeNIHbivy4=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});