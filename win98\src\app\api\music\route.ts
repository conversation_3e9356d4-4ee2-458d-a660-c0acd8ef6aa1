import { NextResponse } from 'next/server';
import { readdir } from 'fs/promises';
import { join } from 'path';

export async function GET() {
  try {
    const musicDir = join(process.cwd(), 'public', 'music');
    const files = await readdir(musicDir);
    
    // Filter only MP3 files
    const mp3Files = files.filter(file => 
      file.toLowerCase().endsWith('.mp3')
    );

    const playlist = mp3Files.map((file, index) => ({
      name: `Song ${index + 1}`,
      url: `/music/${file}`,
      filename: file
    }));

    return NextResponse.json({ playlist });
  } catch (error) {
    console.error('Error reading music directory:', error);
    return NextResponse.json({ playlist: [] });
  }
}
