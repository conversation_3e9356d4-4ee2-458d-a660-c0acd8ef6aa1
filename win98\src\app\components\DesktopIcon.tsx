'use client';

import { useState, useEffect } from 'react';

interface DesktopIconProps {
  icon: string;
  label: string;
  position: { x: number; y: number };
  onDoubleClick: () => void;
}

export default function DesktopIcon({ icon, label, position, onDoubleClick }: DesktopIconProps) {
  const [isSelected, setIsSelected] = useState(false);
  const [clickCount, setClickCount] = useState(0);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent desktop click
    setIsSelected(true);
    setClickCount(prev => prev + 1);

    // Reset click count after 300ms if no second click
    setTimeout(() => {
      if (clickCount === 0) {
        setClickCount(0);
      }
    }, 300);

    // Handle double click
    if (clickCount === 1) {
      onDoubleClick();
      setClickCount(0);
    }
  };

  // Listen for clicks outside to deselect
  useEffect(() => {
    const handleClickOutside = () => {
      setIsSelected(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  return (
    <div
      className={`win98-desktop-icon ${isSelected ? 'selected' : ''}`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
      onClick={handleClick}
      onBlur={() => setIsSelected(false)}
    >
      <div className="win98-icon-image">
        {icon}
      </div>
      <div className="win98-icon-label">
        {label}
      </div>
    </div>
  );
}
