'use client';

import { useState, useRef, useEffect } from 'react';

interface SolitaireProps {
  onClose: () => void;
  initialPosition: { x: number; y: number };
}

interface Card {
  suit: '♠' | '♥' | '♦' | '♣';
  value: string;
  color: 'red' | 'black';
  faceUp: boolean;
  id: string;
}

export default function Solitaire({ onClose, initialPosition }: SolitaireProps) {
  const [position, setPosition] = useState(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [deck, setDeck] = useState<Card[]>([]);
  const [gameStarted, setGameStarted] = useState(false);
  const [tableau, setTableau] = useState<Card[][]>([[], [], [], [], [], [], []]);
  const [stock, setStock] = useState<Card[]>([]);
  const [waste, setWaste] = useState<Card[]>([]);
  const [foundations, setFoundations] = useState<Card[][]>([[], [], [], []]);
  const [score, setScore] = useState(0);
  
  const windowRef = useRef<HTMLDivElement>(null);
  const titleBarRef = useRef<HTMLDivElement>(null);

  const suits: Array<'♠' | '♥' | '♦' | '♣'> = ['♠', '♥', '♦', '♣'];
  const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

  const createDeck = (): Card[] => {
    const newDeck: Card[] = [];
    suits.forEach(suit => {
      values.forEach(value => {
        newDeck.push({
          suit,
          value,
          color: suit === '♥' || suit === '♦' ? 'red' : 'black',
          faceUp: false,
          id: `${suit}-${value}`
        });
      });
    });
    return shuffleDeck(newDeck);
  };

  const shuffleDeck = (deck: Card[]): Card[] => {
    const shuffled = [...deck];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (titleBarRef.current && titleBarRef.current.contains(e.target as Node)) {
      setIsDragging(true);
      const rect = windowRef.current?.getBoundingClientRect();
      if (rect) {
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  const startNewGame = () => {
    const newDeck = createDeck();

    // Deal cards to tableau
    const newTableau: Card[][] = [[], [], [], [], [], [], []];
    let deckIndex = 0;

    for (let col = 0; col < 7; col++) {
      for (let row = 0; row <= col; row++) {
        const card = newDeck[deckIndex++];
        if (row === col) {
          card.faceUp = true; // Top card is face up
        }
        newTableau[col].push(card);
      }
    }

    // Remaining cards go to stock
    const remainingCards = newDeck.slice(deckIndex);

    setTableau(newTableau);
    setStock(remainingCards);
    setWaste([]);
    setFoundations([[], [], [], []]);
    setScore(0);
    setGameStarted(true);
  };

  const CardComponent = ({ card, onClick }: { card: Card; onClick?: () => void }) => (
    <div
      className="solitaire-card"
      onClick={onClick}
      style={{
        width: '50px',
        height: '70px',
        border: '1px solid var(--win98-black)',
        background: card.faceUp ? 'white' : 'var(--win98-blue)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '12px',
        color: card.faceUp ? card.color : 'white',
        cursor: 'pointer',
        margin: '2px'
      }}
    >
      {card.faceUp ? `${card.value}${card.suit}` : '🂠'}
    </div>
  );

  return (
    <div
      ref={windowRef}
      className="win98-window"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: '500px',
        height: '400px',
        zIndex: 100
      }}
      onMouseDown={handleMouseDown}
    >
      <div ref={titleBarRef} className="win98-title-bar">
        <div className="win98-title-text">🃏 Solitaire</div>
        <div className="win98-window-controls">
          <button className="win98-control-button">_</button>
          <button className="win98-control-button">□</button>
          <button className="win98-control-button" onClick={onClose}>×</button>
        </div>
      </div>
      
      <div style={{ 
        padding: '8px', 
        background: 'var(--win98-gray)', 
        height: 'calc(100% - 18px)',
        overflow: 'auto'
      }}>
        {!gameStarted ? (
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: '100%'
          }}>
            <h3 style={{ marginBottom: '20px' }}>Windows Solitaire</h3>
            <button className="win98-button" onClick={startNewGame}>
              New Game
            </button>
            <div style={{ 
              marginTop: '20px', 
              fontSize: '11px', 
              textAlign: 'center',
              color: 'var(--win98-shadow-dark)'
            }}>
              <p>Classic Klondike Solitaire</p>
              <p>Click cards to flip them</p>
              <p>Drag cards to move them</p>
            </div>
          </div>
        ) : (
          <div>
            {/* Game Menu */}
            <div style={{ marginBottom: '10px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <button className="win98-button" onClick={startNewGame}>
                  New Game
                </button>
              </div>
              <div style={{ fontSize: '11px' }}>
                <span>Score: {score}</span>
                <span style={{ marginLeft: '15px' }}>Stock: {stock.length}</span>
                <span style={{ marginLeft: '15px' }}>Waste: {waste.length}</span>
              </div>
            </div>

            {/* Game Area */}
            <div style={{ 
              background: 'var(--win98-desktop)', 
              minHeight: '300px',
              border: '2px inset var(--win98-gray)',
              padding: '10px'
            }}>
              {/* Top Row - Foundation and Stock */}
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
                <div style={{ display: 'flex', gap: '10px' }}>
                  {/* Stock pile */}
                  <div style={{ display: 'flex', gap: '5px' }}>
                    <div
                      className="win98-border-inset"
                      style={{
                        width: '54px',
                        height: '74px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '10px',
                        cursor: stock.length > 0 ? 'pointer' : 'default',
                        background: stock.length > 0 ? 'var(--win98-blue)' : 'var(--win98-gray)'
                      }}
                      onClick={() => {
                        if (stock.length > 0) {
                          // Draw 3 cards from stock to waste
                          const newStock = [...stock];
                          const newWaste = [...waste];

                          for (let i = 0; i < 3 && newStock.length > 0; i++) {
                            const card = newStock.pop()!;
                            card.faceUp = true;
                            newWaste.push(card);
                          }

                          setStock(newStock);
                          setWaste(newWaste);
                        } else if (waste.length > 0) {
                          // Reset: move all waste back to stock
                          const newStock = [...waste].reverse();
                          newStock.forEach(card => card.faceUp = false);
                          setStock(newStock);
                          setWaste([]);
                        }
                      }}
                    >
                      {stock.length > 0 ? '🂠' : '↻'}
                    </div>

                    <div className="win98-border-inset" style={{
                      width: '54px',
                      height: '74px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '10px',
                      position: 'relative'
                    }}>
                      {waste.length > 0 ? (
                        <CardComponent
                          card={waste[waste.length - 1]}
                          onClick={() => {
                            // TODO: Allow moving waste card to foundation or tableau
                            console.log('Clicked waste card');
                          }}
                        />
                      ) : (
                        'Waste'
                      )}
                    </div>
                  </div>
                </div>

                {/* Foundation piles */}
                <div style={{ display: 'flex', gap: '5px' }}>
                  {suits.map((suit, index) => (
                    <div key={index} className="win98-border-inset" style={{ 
                      width: '54px', 
                      height: '74px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '16px'
                    }}>
                      {suit}
                    </div>
                  ))}
                </div>
              </div>

              {/* Tableau */}
              <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
                {tableau.map((column, colIndex) => (
                  <div key={colIndex} style={{
                    display: 'flex',
                    flexDirection: 'column',
                    minHeight: '100px',
                    minWidth: '54px'
                  }}>
                    {column.length === 0 ? (
                      // Empty column placeholder
                      <div className="win98-border-inset" style={{
                        width: '54px',
                        height: '74px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '10px',
                        color: 'var(--win98-shadow-dark)'
                      }}>
                        {colIndex + 1}
                      </div>
                    ) : (
                      column.map((card, cardIndex) => (
                        <div
                          key={card.id}
                          style={{
                            marginTop: cardIndex > 0 ? '-50px' : '0'
                          }}
                        >
                          <CardComponent
                            card={card}
                            onClick={() => {
                              if (!card.faceUp && cardIndex === column.length - 1) {
                                // Flip the top face-down card
                                const newTableau = [...tableau];
                                newTableau[colIndex][cardIndex].faceUp = true;
                                setTableau(newTableau);
                                setScore(score + 5);
                              }
                            }}
                          />
                        </div>
                      ))
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
