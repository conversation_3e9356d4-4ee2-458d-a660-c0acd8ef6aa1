'use client';

import { useState, useEffect } from 'react';

export default function Taskbar() {
  const [currentTime, setCurrentTime] = useState('');
  const [isStartMenuOpen, setIsStartMenuOpen] = useState(false);

  useEffect(() => {
    const updateTime = () => {
      setCurrentTime(new Date().toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }));
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);
    return () => clearInterval(interval);
  }, []);

  const toggleStartMenu = () => {
    setIsStartMenuOpen(!isStartMenuOpen);
  };

  return (
    <>
      {/* Start Menu */}
      {isStartMenuOpen && (
        <div style={{
          position: 'fixed',
          bottom: '28px',
          left: '0',
          width: '200px',
          background: 'var(--win98-gray)',
          border: '2px outset var(--win98-gray)',
          zIndex: 1001
        }}>
          <div style={{
            background: 'linear-gradient(90deg, var(--win98-dark-blue) 0%, var(--win98-blue) 100%)',
            color: 'white',
            padding: '8px',
            fontSize: '14px',
            fontWeight: 'bold'
          }}>
            Windows 98
          </div>

          <div style={{ padding: '4px' }}>
            <div className="start-menu-item">📁 Programs</div>
            <div className="start-menu-item">📄 Documents</div>
            <div className="start-menu-item">⚙️ Settings</div>
            <div className="start-menu-item">🔍 Find</div>
            <div className="start-menu-item">❓ Help</div>
            <div className="start-menu-item">🏃 Run...</div>
            <hr style={{ margin: '4px 0', border: '1px inset var(--win98-gray)' }} />
            <div className="start-menu-item">⏻ Shut Down...</div>
          </div>
        </div>
      )}

      <div className="win98-taskbar">
        <button
          className="win98-start-button"
          onClick={toggleStartMenu}
          style={{
            background: isStartMenuOpen ? 'var(--win98-border-inset)' : 'var(--win98-gray)'
          }}
        >
          <span>🪟</span>
          Start
        </button>

        <div style={{ flex: 1 }}></div>

        <div className="win98-border-inset" style={{
          padding: '2px 8px',
          fontSize: '11px',
          background: 'var(--win98-gray)',
          minWidth: '60px',
          textAlign: 'center'
        }}>
          {currentTime}
        </div>
      </div>

      <style jsx>{`
        .start-menu-item {
          padding: 4px 8px;
          cursor: pointer;
          font-size: 11px;
        }
        .start-menu-item:hover {
          background: var(--win98-highlight);
          color: white;
        }
      `}</style>
    </>
  );
}
