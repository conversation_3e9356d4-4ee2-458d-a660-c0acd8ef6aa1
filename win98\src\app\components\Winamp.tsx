'use client';

import { useState, useRef, useEffect } from 'react';

interface WinampProps {
  onClose: () => void;
  initialPosition: { x: number; y: number };
}

interface Song {
  name: string;
  url: string;
}

export default function Winamp({ onClose, initialPosition }: WinampProps) {
  const [position, setPosition] = useState(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(50);
  const [currentSong, setCurrentSong] = useState(0);
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const windowRef = useRef<HTMLDivElement>(null);
  const titleBarRef = useRef<HTMLDivElement>(null);

  const [playlist, setPlaylist] = useState<Song[]>([]);

  // Load music files from public/music folder
  useEffect(() => {
    const loadPlaylist = async () => {
      try {
        const response = await fetch('/api/music');
        const data = await response.json();

        if (data.playlist && data.playlist.length > 0) {
          setPlaylist(data.playlist);
          console.log(`Found ${data.playlist.length} songs:`, data.playlist.map((s: Song) => s.name));
        } else {
          setPlaylist([{
            name: "No MP3 files found - Add files to /public/music/",
            url: ""
          }]);
        }
      } catch (error) {
        console.error('Error loading playlist:', error);
        setPlaylist([{
          name: "Error loading playlist",
          url: ""
        }]);
      }
    };

    loadPlaylist();
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (titleBarRef.current && titleBarRef.current.contains(e.target as Node)) {
      setIsDragging(true);
      const rect = windowRef.current?.getBoundingClientRect();
      if (rect) {
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
    };
  }, []);

  const togglePlay = () => {
    const audio = audioRef.current;
    const currentSongData = playlist[currentSong];

    if (!audio || !currentSongData || !currentSongData.url) {
      console.log('No valid song to play');
      return;
    }

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play().catch(error => {
        console.error('Error playing audio:', error);
        setIsPlaying(false);
      });
    }
    setIsPlaying(!isPlaying);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseInt(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume / 100;
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const nextSong = () => {
    setCurrentSong((prev) => (prev + 1) % playlist.length);
    setIsPlaying(false);
  };

  const prevSong = () => {
    setCurrentSong((prev) => (prev - 1 + playlist.length) % playlist.length);
    setIsPlaying(false);
  };

  return (
    <div
      ref={windowRef}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: '275px',
        height: '116px',
        zIndex: 100,
        background: 'linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%)',
        border: '2px outset #666',
        borderRadius: '0',
        position: 'absolute'
      }}
      onMouseDown={handleMouseDown}
    >
      {/* Winamp Title Bar */}
      <div ref={titleBarRef} style={{
        background: 'linear-gradient(90deg, #ff6600 0%, #ff3300 100%)',
        color: 'white',
        padding: '2px 4px',
        fontSize: '11px',
        fontWeight: 'bold',
        cursor: 'move',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: '14px'
      }}>
        <span>🎵 Winamp</span>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            fontSize: '12px',
            padding: '0 4px'
          }}
        >×</button>
      </div>

      <div style={{
        padding: '4px',
        background: 'linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%)',
        height: 'calc(100% - 14px)',
        color: '#00ff00'
      }}>
        <audio
          ref={audioRef}
          src={playlist[currentSong]?.url || undefined}
          onEnded={nextSong}
        />
        
        {/* Main Display */}
        <div style={{
          background: '#000',
          color: '#00ff00',
          fontFamily: 'monospace',
          fontSize: '10px',
          padding: '2px 4px',
          height: '20px',
          border: '1px inset #666',
          marginBottom: '4px',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center'
        }}>
          <div style={{ flex: 1, overflow: 'hidden', whiteSpace: 'nowrap' }}>
            {playlist[currentSong]?.name || 'Winamp v2.95'}
          </div>
          <div style={{ minWidth: '50px', textAlign: 'right' }}>
            {formatTime(currentTime)}
          </div>
        </div>

        <div style={{ display: 'flex', gap: '2px' }}>
          {/* Left side - Controls */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
            {/* Transport Controls */}
            <div style={{ display: 'flex', gap: '1px' }}>
              <button
                onClick={prevSong}
                style={{
                  width: '23px', height: '18px', fontSize: '8px',
                  background: 'linear-gradient(135deg, #666 0%, #333 100%)',
                  border: '1px outset #666', color: '#ccc'
                }}
              >⏮</button>
              <button
                onClick={togglePlay}
                style={{
                  width: '23px', height: '18px', fontSize: '8px',
                  background: 'linear-gradient(135deg, #666 0%, #333 100%)',
                  border: '1px outset #666', color: '#ccc'
                }}
              >{isPlaying ? '⏸' : '▶'}</button>
              <button
                onClick={() => { setIsPlaying(false); if(audioRef.current) audioRef.current.pause(); }}
                style={{
                  width: '23px', height: '18px', fontSize: '8px',
                  background: 'linear-gradient(135deg, #666 0%, #333 100%)',
                  border: '1px outset #666', color: '#ccc'
                }}
              >⏹</button>
              <button
                onClick={nextSong}
                style={{
                  width: '23px', height: '18px', fontSize: '8px',
                  background: 'linear-gradient(135deg, #666 0%, #333 100%)',
                  border: '1px outset #666', color: '#ccc'
                }}
              >⏭</button>
            </div>

            {/* Volume */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
              <span style={{ fontSize: '8px', color: '#ccc', minWidth: '20px' }}>Vol</span>
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={handleVolumeChange}
                style={{
                  width: '60px',
                  height: '10px',
                  background: '#333'
                }}
              />
            </div>
          </div>

          {/* Right side - Spectrum/Visualization placeholder */}
          <div style={{
            flex: 1,
            background: '#000',
            border: '1px inset #666',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '8px',
            color: '#00ff00'
          }}>
            {isPlaying ? '♪♫♪♫♪' : '- - -'}
          </div>
        </div>
      </div>
    </div>
  );
}
