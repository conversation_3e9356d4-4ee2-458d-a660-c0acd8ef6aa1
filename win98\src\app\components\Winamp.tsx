'use client';

import { useState, useRef, useEffect } from 'react';

interface WinampProps {
  onClose: () => void;
  initialPosition: { x: number; y: number };
}

interface Song {
  name: string;
  url: string;
}

export default function Winamp({ onClose, initialPosition }: WinampProps) {
  const [position, setPosition] = useState(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(50);
  const [currentSong, setCurrentSong] = useState(0);
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const windowRef = useRef<HTMLDivElement>(null);
  const titleBarRef = useRef<HTMLDivElement>(null);

  // Sample songs - you can add MP3 files to public/music/ folder
  const playlist: Song[] = [
    { name: "Sample Song 1", url: "/music/sample1.mp3" },
    { name: "Sample Song 2", url: "/music/sample2.mp3" },
    { name: "Sample Song 3", url: "/music/sample3.mp3" },
  ];

  const handleMouseDown = (e: React.MouseEvent) => {
    if (titleBarRef.current && titleBarRef.current.contains(e.target as Node)) {
      setIsDragging(true);
      const rect = windowRef.current?.getBoundingClientRect();
      if (rect) {
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragOffset]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
    };
  }, []);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play().catch(console.error);
    }
    setIsPlaying(!isPlaying);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseInt(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume / 100;
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const nextSong = () => {
    setCurrentSong((prev) => (prev + 1) % playlist.length);
    setIsPlaying(false);
  };

  const prevSong = () => {
    setCurrentSong((prev) => (prev - 1 + playlist.length) % playlist.length);
    setIsPlaying(false);
  };

  return (
    <div
      ref={windowRef}
      className="win98-window"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        width: '300px',
        height: '200px',
        zIndex: 100
      }}
      onMouseDown={handleMouseDown}
    >
      <div ref={titleBarRef} className="win98-title-bar">
        <div className="win98-title-text">🎵 Winamp</div>
        <div className="win98-window-controls">
          <button className="win98-control-button">_</button>
          <button className="win98-control-button">□</button>
          <button className="win98-control-button" onClick={onClose}>×</button>
        </div>
      </div>
      
      <div style={{ padding: '8px', background: 'var(--win98-gray)', height: 'calc(100% - 18px)' }}>
        <audio
          ref={audioRef}
          src={playlist[currentSong]?.url}
          onEnded={nextSong}
        />
        
        {/* Display */}
        <div className="win98-border-inset" style={{ 
          padding: '4px', 
          background: '#000', 
          color: '#00ff00', 
          fontFamily: 'monospace',
          fontSize: '12px',
          marginBottom: '8px',
          height: '40px'
        }}>
          <div>{playlist[currentSong]?.name || 'No song loaded'}</div>
          <div>{formatTime(currentTime)} / {formatTime(duration)}</div>
        </div>

        {/* Controls */}
        <div style={{ display: 'flex', gap: '4px', marginBottom: '8px' }}>
          <button className="win98-button" onClick={prevSong}>⏮️</button>
          <button className="win98-button" onClick={togglePlay}>
            {isPlaying ? '⏸️' : '▶️'}
          </button>
          <button className="win98-button" onClick={nextSong}>⏭️</button>
          <button className="win98-button">⏹️</button>
        </div>

        {/* Volume */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '10px' }}>Vol:</span>
          <input
            type="range"
            min="0"
            max="100"
            value={volume}
            onChange={handleVolumeChange}
            style={{ flex: 1 }}
          />
          <span style={{ fontSize: '10px', minWidth: '30px' }}>{volume}%</span>
        </div>

        {/* Instructions */}
        <div style={{ 
          fontSize: '9px', 
          marginTop: '8px', 
          color: 'var(--win98-shadow-dark)',
          textAlign: 'center'
        }}>
          Add MP3 files to /public/music/ folder
        </div>
      </div>
    </div>
  );
}
