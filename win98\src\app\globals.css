@import "tailwindcss";

/* Windows 98 Color Palette */
:root {
  --win98-gray: #c0c0c0;
  --win98-dark-gray: #808080;
  --win98-light-gray: #dfdfdf;
  --win98-white: #ffffff;
  --win98-black: #000000;
  --win98-blue: #0000ff;
  --win98-dark-blue: #000080;
  --win98-desktop: #008080;
  --win98-highlight: #316ac5;
  --win98-shadow-dark: #404040;
  --win98-shadow-light: #ffffff;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  background: var(--win98-desktop);
  overflow: hidden;
  height: 100vh;
  cursor: default;
  user-select: none;
}

/* Windows 98 3D Border Effect */
.win98-border {
  border: 2px solid;
  border-color: var(--win98-light-gray) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-light-gray);
}

.win98-border-inset {
  border: 2px solid;
  border-color: var(--win98-shadow-dark) var(--win98-light-gray) var(--win98-light-gray) var(--win98-shadow-dark);
}

.win98-border-raised {
  border: 1px solid;
  border-color: var(--win98-white) var(--win98-dark-gray) var(--win98-dark-gray) var(--win98-white);
}

/* Button styles */
.win98-button {
  background: var(--win98-gray);
  border: 2px solid;
  border-color: var(--win98-white) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-white);
  padding: 2px 8px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  cursor: pointer;
  outline: none;
}

.win98-button:active {
  border-color: var(--win98-shadow-dark) var(--win98-white) var(--win98-white) var(--win98-shadow-dark);
  padding: 3px 7px 1px 9px;
}

.win98-button:focus {
  outline: 1px dotted var(--win98-black);
  outline-offset: -4px;
}

/* Window styles */
.win98-window {
  background: var(--win98-gray);
  border: 2px solid;
  border-color: var(--win98-light-gray) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-light-gray);
  position: absolute;
  min-width: 200px;
  min-height: 100px;
}

.win98-title-bar {
  background: linear-gradient(90deg, var(--win98-dark-blue) 0%, var(--win98-blue) 100%);
  color: var(--win98-white);
  padding: 2px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 18px;
  cursor: move;
}

.win98-title-bar.inactive {
  background: var(--win98-dark-gray);
}

.win98-title-text {
  padding-left: 4px;
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
}

.win98-window-controls {
  display: flex;
  gap: 2px;
  padding-right: 2px;
}

.win98-control-button {
  width: 16px;
  height: 14px;
  background: var(--win98-gray);
  border: 1px solid;
  border-color: var(--win98-white) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-white);
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--win98-black);
}

.win98-control-button:active {
  border-color: var(--win98-shadow-dark) var(--win98-white) var(--win98-white) var(--win98-shadow-dark);
}

/* Desktop styles */
.win98-desktop {
  width: 100vw;
  height: 100vh;
  background: var(--win98-desktop);
  position: relative;
  overflow: hidden;
}

/* Taskbar styles */
.win98-taskbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 28px;
  background: var(--win98-gray);
  border-top: 1px solid var(--win98-light-gray);
  display: flex;
  align-items: center;
  padding: 2px;
  z-index: 1000;
}

.win98-start-button {
  background: var(--win98-gray);
  border: 2px solid;
  border-color: var(--win98-white) var(--win98-shadow-dark) var(--win98-shadow-dark) var(--win98-white);
  padding: 2px 8px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  height: 22px;
}

.win98-start-button:active {
  border-color: var(--win98-shadow-dark) var(--win98-white) var(--win98-white) var(--win98-shadow-dark);
}

/* Desktop icon styles */
.win98-desktop-icon {
  position: absolute;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 6px;
  border: 1px solid transparent;
}

.win98-desktop-icon:hover {
  background: rgba(255, 255, 255, 0.1);
}

.win98-desktop-icon.selected {
  background: var(--win98-highlight);
  border: 1px dotted var(--win98-white);
}

.win98-icon-image {
  width: 48px;
  height: 48px;
  background: transparent;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  filter: drop-shadow(1px 1px 1px rgba(0,0,0,0.5));
}

.win98-icon-label {
  color: var(--win98-white);
  font-size: 11px;
  text-align: center;
  text-shadow: 1px 1px 1px var(--win98-black);
  word-wrap: break-word;
  max-width: 75px;
  line-height: 1.2;
}

/* Chat window specific styles */
.win98-chat-window {
  width: 400px;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.win98-chat-content {
  flex: 1;
  background: var(--win98-white);
  border: 2px solid;
  border-color: var(--win98-shadow-dark) var(--win98-light-gray) var(--win98-light-gray) var(--win98-shadow-dark);
  margin: 2px;
  padding: 8px;
  overflow-y: auto;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
}

.win98-chat-input-area {
  background: var(--win98-gray);
  padding: 4px;
  border-top: 1px solid var(--win98-shadow-dark);
  display: flex;
  gap: 4px;
}

.win98-chat-input {
  flex: 1;
  border: 2px solid;
  border-color: var(--win98-shadow-dark) var(--win98-light-gray) var(--win98-light-gray) var(--win98-shadow-dark);
  padding: 2px 4px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  background: var(--win98-white);
}

.win98-chat-input:focus {
  outline: none;
}

/* Message styles */
.chat-message {
  margin-bottom: 8px;
  padding: 4px;
}

.chat-message.user {
  text-align: right;
  background: #e6f3ff;
  border: 1px solid #b3d9ff;
}

.chat-message.assistant {
  text-align: left;
  background: #f0f0f0;
  border: 1px solid #d0d0d0;
}

.message-sender {
  font-weight: bold;
  margin-bottom: 2px;
  font-size: 10px;
}

.message-content {
  line-height: 1.3;
}
