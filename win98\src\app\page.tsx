'use client';

import { useState } from 'react';
import DesktopIcon from './components/DesktopIcon';
import ChatWindow from './components/ChatWindow';
import Taskbar from './components/Taskbar';
import Winamp from './components/Winamp';
import Solitaire from './components/Solitaire';

export default function Home() {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isWinampOpen, setIsWinampOpen] = useState(false);
  const [isSolitaireOpen, setIsSolitaireOpen] = useState(false);

  const handleIconDoubleClick = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleCloseWinamp = () => {
    setIsWinampOpen(false);
  };

  const handleCloseSolitaire = () => {
    setIsSolitaireOpen(false);
  };

  return (
    <div className="win98-desktop">
      {/* Desktop Icons */}
      <DesktopIcon
        icon="💬"
        label="Chat with <PERSON>"
        position={{ x: 50, y: 50 }}
        onDoubleClick={handleIconDoubleClick}
      />

      <DesktopIcon
        icon="🖥️"
        label="My Computer"
        position={{ x: 50, y: 140 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="📂"
        label="My Documents"
        position={{ x: 50, y: 230 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="🗑️"
        label="Recycle Bin"
        position={{ x: 50, y: 320 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="🌍"
        label="Internet Explorer"
        position={{ x: 50, y: 410 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="✉️"
        label="Outlook Express"
        position={{ x: 150, y: 50 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="🎵"
        label="Winamp"
        position={{ x: 150, y: 140 }}
        onDoubleClick={() => setIsWinampOpen(true)}
      />

      <DesktopIcon
        icon="🃏"
        label="Solitaire"
        position={{ x: 150, y: 230 }}
        onDoubleClick={() => setIsSolitaireOpen(true)}
      />

      <DesktopIcon
        icon="🖌️"
        label="Paint"
        position={{ x: 150, y: 320 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="📄"
        label="Notepad"
        position={{ x: 150, y: 410 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="⚙️"
        label="Control Panel"
        position={{ x: 250, y: 50 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="💾"
        label="3½ Floppy (A:)"
        position={{ x: 250, y: 140 }}
        onDoubleClick={() => {}}
      />

      <DesktopIcon
        icon="💿"
        label="CD-ROM (D:)"
        position={{ x: 250, y: 230 }}
        onDoubleClick={() => {}}
      />

      {/* Chat Window */}
      {isChatOpen && (
        <ChatWindow
          title="Chat with Sheila"
          onClose={handleCloseChat}
          initialPosition={{ x: 200, y: 100 }}
        />
      )}

      {/* Winamp */}
      {isWinampOpen && (
        <Winamp
          onClose={handleCloseWinamp}
          initialPosition={{ x: 300, y: 150 }}
        />
      )}

      {/* Solitaire */}
      {isSolitaireOpen && (
        <Solitaire
          onClose={handleCloseSolitaire}
          initialPosition={{ x: 250, y: 80 }}
        />
      )}

      {/* Taskbar */}
      <Taskbar />
    </div>
  );
}
